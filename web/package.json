{"name": "web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"@internationalized/date": "^3.8.2", "@lucide/svelte": "^0.515.0", "@sveltejs/vite-plugin-svelte": "^5.1.0", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "@tanstack/table-core": "^8.21.3", "bits-ui": "^2.8.10", "clsx": "^2.1.1", "embla-carousel-svelte": "^8.6.0", "formsnap": "^2.0.1", "layerchart": "2.0.0-next.27", "mode-watcher": "^1.1.0", "paneforge": "^1.0.0", "phosphor-svelte": "^3.0.1", "svelte-sonner": "^1.0.5", "sveltekit-superforms": "^2.27.1", "tailwind-merge": "^3.3.1", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.0.0", "tw-animate-css": "^1.3.5", "vaul-svelte": "1.0.0-next.7", "vite": "^7.0.3"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}, "dependencies": {"@inertiajs/svelte": "^2.0.14", "svelte": "^5.35.5"}}