@import "tailwindcss";

@import "tw-animate-css";
@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';

@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0;
  --background: oklch(0.95 0.03 85);
  --foreground: oklch(0.15 0 0);
  --card: oklch(0.98 0.02 85);
  --card-foreground: oklch(0.15 0 0);
  --popover: oklch(0.98 0.02 85);
  --popover-foreground: oklch(0.15 0 0);
  --primary: oklch(0.25 0 0);
  --primary-foreground: oklch(0.95 0.03 85);
  --secondary: oklch(0.92 0.025 85);
  --secondary-foreground: oklch(0.25 0 0);
  --muted: oklch(0.92 0.025 85);
  --muted-foreground: oklch(0.5 0 0);
  --accent: oklch(0.92 0.025 85);
  --accent-foreground: oklch(0.25 0 0);
  --destructive: oklch(0.55 0.15 25);
  --border: oklch(0.88 0.02 85);
  --input: oklch(0.88 0.02 85);
  --ring: oklch(0.5 0 0);
  --chart-1: oklch(0.4 0.15 180);
  --chart-2: oklch(0.5 0.15 240);
  --chart-3: oklch(0.6 0.15 120);
  --chart-4: oklch(0.7 0.15 60);
  --chart-5: oklch(0.8 0.15 300);
  --sidebar: oklch(0.94 0.025 85);
  --sidebar-foreground: oklch(0.2 0 0);
  --sidebar-primary: oklch(0.25 0 0);
  --sidebar-primary-foreground: oklch(0.95 0.03 85);
  --sidebar-accent: oklch(0.9 0.02 85);
  --sidebar-accent-foreground: oklch(0.25 0 0);
  --sidebar-border: oklch(0.86 0.02 85);
  --sidebar-ring: oklch(0.5 0 0);
}

.dark {
  --background: oklch(0.08 0 0);
  --foreground: oklch(0.95 0 0);
  --card: oklch(0.12 0 0);
  --card-foreground: oklch(0.95 0 0);
  --popover: oklch(0.12 0 0);
  --popover-foreground: oklch(0.95 0 0);
  --primary: oklch(0.85 0 0);
  --primary-foreground: oklch(0.08 0 0);
  --secondary: oklch(0.18 0 0);
  --secondary-foreground: oklch(0.85 0 0);
  --muted: oklch(0.18 0 0);
  --muted-foreground: oklch(0.65 0 0);
  --accent: oklch(0.18 0 0);
  --accent-foreground: oklch(0.85 0 0);
  --destructive: oklch(0.55 0.15 25);
  --border: oklch(0.25 0 0);
  --input: oklch(0.25 0 0);
  --ring: oklch(0.5 0 0);
  --chart-1: oklch(0.6 0.15 180);
  --chart-2: oklch(0.7 0.15 240);
  --chart-3: oklch(0.8 0.15 120);
  --chart-4: oklch(0.9 0.15 60);
  --chart-5: oklch(0.85 0.15 300);
  --sidebar: oklch(0.1 0 0);
  --sidebar-foreground: oklch(0.9 0 0);
  --sidebar-primary: oklch(0.85 0 0);
  --sidebar-primary-foreground: oklch(0.08 0 0);
  --sidebar-accent: oklch(0.2 0 0);
  --sidebar-accent-foreground: oklch(0.85 0 0);
  --sidebar-border: oklch(0.22 0 0);
  --sidebar-ring: oklch(0.5 0 0);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
