<script module>
  import DefaultLayout from "../../layouts/DefaultLayout.svelte";
  export const layout = DefaultLayout;
</script>

<script lang="ts">
  import Button from "$lib/components/ui/button/button.svelte";

  let { inertia, abbas, query_params, current_url, current_path, query_string } = $props();

  // Query params are now available through the page store via our middleware.
  // This is a workaround to get the query params in the frontend.
  let queryParams = $state(query_params || {});
  let currentUrl = $state(current_url || '');
  let currentPath = $state(current_path || '');
  let queryString = $state(query_string || '');
</script>

<main
  class="container mx-auto flex flex-col items-center justify-center h-screen space-y-4"
>

  <Button class="bg-blue-500 text-white" variant="default" disabled={false}
    >Click me</Button
  >
  <h1 class="text-3xl font-bold underline">Welcome to DnD clinic mgmt system</h1>
  <p>Message from the backend: <strong>{inertia}</strong></p>

  <div class="text-lg space-y-2">
    <p>Name: <strong>{abbas.name}</strong></p>
    <p>Age: <strong>{abbas.age}</strong></p>
    <p>City: <strong>{abbas.city}</strong></p>
  </div>

  <!-- Query Parameters Demo Section -->
  <div class="bg-gray-100 p-6 rounded-lg max-w-2xl w-full">
    <h2 class="text-xl font-semibold mb-4">Query Parameters (via Middleware)</h2>
    
    <div class="space-y-3 text-sm">
      <div>
        <strong>Current URL:</strong> 
        <code class="bg-gray-200 px-2 py-1 rounded text-xs">{currentUrl}</code>
      </div>
      
      <div>
        <strong>Current Path:</strong> 
        <code class="bg-gray-200 px-2 py-1 rounded text-xs">{currentPath}</code>
      </div>
      
      <div>
        <strong>Query String:</strong> 
        <code class="bg-gray-200 px-2 py-1 rounded text-xs">{queryString || 'None'}</code>
      </div>
      
      <div>
        <strong>Parsed Query Parameters:</strong>
        {#if Object.keys(queryParams).length > 0}
          <ul class="mt-2 space-y-1">
            {#each Object.entries(queryParams) as [key, value]}
              <li class="flex">
                <span class="font-medium w-20">{key}:</span>
                <code class="bg-gray-200 px-2 py-1 rounded text-xs flex-1">
                  {Array.isArray(value) ? value.join(', ') : value}
                </code>
              </li>
            {/each}
          </ul>
        {:else}
          <p class="text-gray-500 mt-2">No query parameters present</p>
        {/if}
      </div>
    </div>
    
    <div class="mt-4 pt-4 border-t border-gray-300">
      <p class="text-xs text-gray-600">
        <strong>Try it:</strong> Add query parameters to the URL like 
        <code class="bg-gray-200 px-1 rounded">?name=John&age=25&tags=developer&tags=svelte</code>
      </p>
    </div>
  </div>
</main>
