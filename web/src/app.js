import { createInertiaApp } from '@inertiajs/svelte'
import { mount } from 'svelte'
import DefaultLayout from './layouts/DefaultLayout.svelte'

createInertiaApp({
  id: 'app',
  resolve: name => {
    const pages = import.meta.glob('./pages/**/*.svelte', { eager: true })
    let page = pages[`./pages/${name}.svelte`]
    return {
      default: page.default,
      layout: page.layout || DefaultLayout,
    }
  },
  setup({ el, App, props }) {
    mount(App, { target: el, props })
  },
})