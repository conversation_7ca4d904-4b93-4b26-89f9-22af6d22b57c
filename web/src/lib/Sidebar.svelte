<script lang="ts">
  import * as Drawer from "$lib/components/ui/drawer/index.js";
  import * as Sidebar from "$lib/components/ui/sidebar/index.js";
  import { IsMobile } from "$lib/hooks/is-mobile.svelte.js";
  import { Link } from "@inertiajs/svelte";
  import {
    Bell,
    ChartBar,
    Folder,
    Gear,
    House,
    Users,
    Sidebar as SidebarIcon,
  } from "phosphor-svelte";

  // Define types for our sidebar data
  interface SidebarItem {
    title: string;
    icon: any; // Phosphor icon component
    url: string;
    isActive?: boolean;
    badge?: string | number;
  }

  interface SidebarSection {
    title: string;
    items: SidebarItem[];
  }

  // Props
  let {
    sections = [],
    children,
    variant = "sidebar",
    side = "left",
    collapsible = "offcanvas",
    open = $bindable(false),
  }: {
    sections?: SidebarSection[];
    children?: any;
    variant?: "sidebar" | "floating" | "inset";
    side?: "left" | "right";
    collapsible?: "offcanvas" | "icon" | "none";
    open?: boolean;
  } = $props();

  // Default sections if none provided
  const defaultSections: SidebarSection[] = [
    {
      title: "Main",
      items: [
        { title: "Dashboard", icon: House, url: "/dashboard", isActive: true },
        { title: "Analytics", icon: ChartBar, url: "/analytics" },
        { title: "Users", icon: Users, url: "/users", badge: "12" },
      ],
    },
    {
      title: "Content",
      items: [
        { title: "Projects", icon: Folder, url: "/projects" },
        {
          title: "Notifications",
          icon: Bell,
          url: "/notifications",
          badge: "3",
        },
      ],
    },
    {
      title: "System",
      items: [{ title: "Settings", icon: Gear, url: "/settings" }],
    },
  ];

  const isMobile = new IsMobile();
  const finalSections = sections.length > 0 ? sections : defaultSections;
</script>

{#if isMobile.current}
  <!-- Mobile: Use Drawer -->
  <Drawer.Root bind:open>
    <Drawer.Trigger>
      <button
        class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10"
      >
        <SidebarIcon class="h-4 w-4" />
        <span class="sr-only">Toggle menu</span>
      </button>
    </Drawer.Trigger>

    <Drawer.Content class="h-[96%]">
      <Drawer.Header>
        <Drawer.Title class="flex items-center gap-2">
          <House weight="duotone" class="h-5 w-5" />
          Your App
        </Drawer.Title>
      </Drawer.Header>

      <div class="flex-1 overflow-y-auto p-4">
        {#each finalSections as section}
          <div class="mb-6">
            <h3
              class="mb-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider"
            >
              {section.title}
            </h3>
            <nav class="space-y-1">
              {#each section.items as item}
                {@const IconComponent = item.icon}
                <Link
                  href={item.url}
                  class="flex w-full items-center gap-3 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none transition-colors"
                >
                  <IconComponent class="h-4 w-4" weight="duotone" />
                  <span class="flex-1 text-left">{item.title}</span>
                  {#if item.badge}
                    <span
                      class="rounded-full bg-primary px-2 py-0.5 text-xs text-primary-foreground"
                    >
                      {item.badge}
                    </span>
                  {/if}
                </Link>
              {/each}
            </nav>
          </div>
        {/each}
      </div>

      <Drawer.Footer class="border-t">
        <div class="text-xs text-muted-foreground text-center">
          © 2024 Your App
        </div>
      </Drawer.Footer>
    </Drawer.Content>
  </Drawer.Root>
{:else}
  <!-- Desktop: Use Sidebar -->
  <Sidebar.Provider>
    <Sidebar.Root {variant} {side} {collapsible} class="border-gray-400">
      <Sidebar.Header>
        <div class="flex items-center gap-2 px-2 py-2">
          <div
            class="flex h-8 w-8 items-center justify-center rounded-md text-black"
          >
            <House weight="duotone" class="h-4 w-4" />
          </div>
          <span class="font-semibold">Your App</span>
        </div>
      </Sidebar.Header>

      <Sidebar.Content class="flex flex-col gap-2">
        {#each finalSections as section}
          <Sidebar.Group>
            <Sidebar.GroupLabel
              class="text-xs font-semibold text-muted-foreground uppercase tracking-wider"
            >
              {section.title}
            </Sidebar.GroupLabel>
            <Sidebar.GroupContent>
              <Sidebar.Menu>
                {#each section.items as item}
                  {@const IconComponent = item.icon}
                  <Sidebar.MenuItem>
                    <Sidebar.MenuButton isActive={item.isActive || false}>
                      {#snippet child({ props })}
                        <Link
                          {...props}
                          href={item.url}
                          class="flex w-full items-center gap-3 rounded-md px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active=true]:bg-accent data-[active=true]:text-accent-foreground transition-colors"
                        >
                          <IconComponent class="h-4 w-4" weight="duotone" />
                          <span class="flex-1 text-left">{item.title}</span>
                          {#if item.badge}
                            <Sidebar.MenuBadge>{item.badge}</Sidebar.MenuBadge>
                          {/if}
                        </Link>
                      {/snippet}
                    </Sidebar.MenuButton>
                  </Sidebar.MenuItem>
                {/each}
              </Sidebar.Menu>
            </Sidebar.GroupContent>
          </Sidebar.Group>
          {#if section !== finalSections[finalSections.length - 1]}
            <Sidebar.Separator class="border-gray-400 border-t" />
          {/if}
        {/each}
      </Sidebar.Content>

      <Sidebar.Footer>
        <div class="p-2 text-xs text-muted-foreground">© 2024 Your App</div>
      </Sidebar.Footer>
    </Sidebar.Root>

    {#if children}
      <Sidebar.Inset>
        <main class="flex-1">
          <Sidebar.Trigger />
          {@render children()}
        </main>
      </Sidebar.Inset>
    {/if}
  </Sidebar.Provider>
{/if}
