from django.db import models
from django.utils.translation import gettext_lazy as _




class MonthChoices(models.TextChoices):
    ONE = 'كانون الثاني', _('كانون الثاني / 1')
    TWO = 'شباط', _('شباط / 2')
    THREE = 'آذار', _('آذار / 3')
    FOUR = 'نيسان', _('نيسان / 4')
    FIVE = 'أيار', _('أيار / 5')
    SIX = 'حزيران', _('حزيران / 6')
    SEVEN = 'تموز', _('تموز / 7')
    EIGHT = 'آب', _('آب / 8')
    NINE = 'أيلول', _('أيلول / 9')
    TEN = 'تشرين الأول', _('تشرين الأول / 10')
    ELEVEN = 'تشرين الثاني', _('تشرين الثاني / 11')
    TWELVE = 'كانون الأول', _('كانون الأول / 12')


month_choices = {
    '1': _('كانون الثاني'),
    '2': _('شباط'),
    '3': _('آذار'),
    '4': _('نيسان'),
    '5': _('أيار'),
    '6': _('حزيران'),
    '7': _('تموز'),
    '8': _('آب'),
    '9': _('أيلول'),
    '10': _('تشرين الأول'),
    '11': _('تشرين الثاني'),
    '12': _('كانون الأول'),
}

weekday_choices = {
    '6': _('الأحد'),
    '0': _('الاثنين'),
    '1': _('الثلاثاء'),
    '2': _('الأربعاء'),
    '3': _('الخميس'),
    '4': _('الجمعة'),
    '5': _('السبت'),
}

month_choices_reverse = {
    _('كانون الثاني'): '1',
    _('شباط'): '2',
    _('آذار'): '3',
    _('نيسان'): '4',
    _('أيار'): '5',
    _('حزيران'): '6',
    _('تموز'): '7',
    _('آب'): '8',
    _('أيلول'): '9',
    _('تشرين الأول'): '10',
    _('تشرين الثاني'): '11',
    _('كانون الأول'): '12',
}


class WeekDays(models.TextChoices):
    SAT = 'السبت', _('السبت')
    SUN = 'الأحد', _('الأحد')
    MON = 'الاثنين', _('الاثنين')
    TUE = 'الثلاثاء', _('الثلاثاء')
    WED = 'الأربعاء', _('الأربعاء')
    THU = 'الخميس', _('الخميس')
    FRI = 'الجمعة', _('الجمعة')


class Gender(models.TextChoices):
    MALE = 'ذكر', _('ذكر')
    FEMALE = 'أنثى', _('أنثى')
