from django.db import models
from django_multitenant.utils import get_current_tenant
import uuid
from abstract.models.core import TenantMixin


class SingletonModel(TenantMixin):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, editable=True)
    updated_at = models.DateTimeField(editable=False, auto_now=True)

    class Meta(TenantMixin.Meta):
        abstract = True

    def delete(self, *args, **kwargs):
        pass  # This method is deliberately empty, we don't want to delete the singleton

    @classmethod
    def load(cls, tenant=None):
        if not tenant:
            tenant = get_current_tenant()
        obj, created = cls.objects.get_or_create(id=tenant.id, ten=tenant)
        return obj
