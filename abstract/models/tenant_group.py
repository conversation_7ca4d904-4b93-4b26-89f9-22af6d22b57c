# auto_prefetch removed; using standard Django ORM.
import auto_prefetch
from django.contrib.auth.models import Group, GroupManager
from django.db import models
from django.db.models import UniqueConstraint
from django.urls import reverse_lazy
from django_lifecycle import hook, BEFORE_SAVE
from django_multitenant.mixins import TenantManagerMixin

from abstract.models.core import IntEntity
from django.utils.translation import gettext_lazy as _

Group.add_to_class('__str__', lambda self: f'{self.name.split("_")[-1]}' if '_' in self.name else self.name)


class TenantGroupManager(GroupManager, TenantManagerMixin, auto_prefetch.Manager):
    pass


class TenantGroup(IntEntity):
    group_name = models.CharField(verbose_name=_('الاسم'), max_length=255)
    group = models.ForeignKey(Group, verbose_name=_('المجموعة'), on_delete=models.CASCADE, null=True,
                              blank=True, related_name='tenant_groups')

    objects = TenantGroupManager()

    class Meta(auto_prefetch.Model.Meta):
        verbose_name = _('مجموعة الصلاحيات')
        verbose_name_plural = _('مجموعات الصلاحيات')
        constraints = [
            UniqueConstraint(fields=['group_name', 'ten'], name='unique_group_name_tenant'),
        ]

        default_permissions = ()


    def __str__(self):
        return f'{self.group_name}'

    @hook(BEFORE_SAVE)
    def set_group_name(self):
        self.group.name = f'{self.ten.name}_{self.group_name}'
        self.group.save()
