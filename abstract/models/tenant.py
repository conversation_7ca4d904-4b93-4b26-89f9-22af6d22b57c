import decimal
import uuid

from django.contrib.auth.hashers import make_password
from django.db import models
from django.utils.translation import gettext_lazy as _
from django_multitenant.mixins import TenantModelMixin, TenantManagerMixin

from abstract.utils import generate_snowflake_id


class TenantManager(TenantManagerMixin):
    pass


class Tenant(TenantModelMixin, models.Model):
    id = models.UUIDField(default=uuid.uuid4, primary_key=True, editable=False)
    tenant_id = 'id'
    uuid = models.UUIDField(default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(editable=False, auto_now_add=True)
    updated_at = models.DateTimeField(editable=False, auto_now=True)
    extra = models.JSONField(null=True, blank=True, default=dict)
    first_run = models.BooleanField(
        'First Run',
        default=False,
    )
    name = models.CharField(
        'Tenant Name',
        max_length=30,
        blank=False,
    )
    sub_start = models.DateTimeField(
        'Subscription Start Date',
        blank=True,
        null=True,
    )
    sub_end = models.DateTimeField(
        'Subscription End Date',
        blank=True,
        null=True,
    )
    sub_status = models.BooleanField(
        'Subscription Status',
        default=False,
    )
    trial_start = models.DateTimeField(
        'Trial Start Date',
        blank=True,
        null=True,
    )
    trial_end = models.DateTimeField(
        'Trial End Date',
        blank=True,
        null=True,
    )

    class Meta:
        verbose_name = 'Tenant'
        verbose_name_plural = 'Tenants'

    def __str__(self):
        return self.name


class BillingTypes(models.TextChoices):
    MONTHLY = 'Monthly', _('Monthly')
    ANNUAL = 'Annual', _('Annual')


class Billing(models.Model):
    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='billings',
    )
    type = models.CharField(
        'Billing Type',
        max_length=30,
        blank=True,
        default=BillingTypes.MONTHLY,
        choices=BillingTypes.choices,
    )
    amount_due = models.DecimalField(
        'Amount Due',
        max_digits=10,
        decimal_places=2,
    )
    due_date = models.DateTimeField(
        'Due Date',
        blank=True,
        null=True,
    )
    grace_period = models.IntegerField(
        'Grace Period (in days)',
        default=0,
    )
    amount_paid = models.DecimalField(
        'Amount Paid',
        max_digits=10,
        decimal_places=2,
        default=0.00
    )
    date_paid = models.DateTimeField(
        'Payment Date',
        blank=True,
        null=True,
    )

    class Meta:
        verbose_name = 'Billing'
        verbose_name_plural = 'Billings'

    def __str__(self):
        return f'{self.tenant.name} - {self.due_date}'


class APIKey(models.Model):
    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='api_keys',
    )
    key = models.CharField(
        'API Key',
        max_length=128,
        editable=False,
        unique=True,
    )
    created_at = models.DateTimeField(
        'Date Created',
        auto_now_add=True,
    )
    active = models.BooleanField(
        'Active',
        default=True,
    )

    class Meta:
        verbose_name = 'API Key'
        verbose_name_plural = 'API Keys'

    def __str__(self):
        return f'{self.tenant.name} - {self.key}'

    def save(self, *args, **kwargs):
        if not self.pk:  # Only hash key when creating object
            self.key = make_password(str(uuid.uuid4()))
        super().save(*args, **kwargs)


class APIBilling(models.Model):
    api_key = models.ForeignKey(
        APIKey,
        on_delete=models.CASCADE,
        related_name='api_billings',
    )
    requests_made = models.PositiveIntegerField(
        'Requests Made',
        default=0,
    )
    amount_due = models.DecimalField(
        'Amount Due',
        max_digits=19,
        decimal_places=2,
        default=decimal.Decimal(0)
    )
    billing_start = models.DateTimeField(
        'Billing Start Date',
        blank=True,
        null=True,
    )
    billing_end = models.DateTimeField(
        'Billing End Date',
        blank=True,
        null=True,
    )
    due_date = models.DateTimeField(
        'Due Date',
        blank=True,
        null=True,
    )
    amount_paid = models.DecimalField(
        'Amount Paid',
        max_digits=19,
        decimal_places=2,
        default=decimal.Decimal(0)
    )
    date_paid = models.DateTimeField(
        'Payment Date',
        blank=True,
        null=True,
    )

    class Meta:
        verbose_name = 'API Billing'
        verbose_name_plural = 'API Billings'

    def __str__(self):
        return f'{self.api_key.tenant.name} - {self.requests_made} - {self.due_date}'
