import uuid

import auto_prefetch
from django.db import models
from django.utils.html import format_html
from django_currentuser.db.models import Current<PERSON>serField
from django_lifecycle import LifecycleModel, hook, BEFORE_UPDATE, AFTER_CREATE, BEFORE_DELETE
from django_multitenant.fields import TenantF<PERSON>ign<PERSON>ey
from django_multitenant.mixins import TenantModelMixin, TenantManagerMixin
from django.conf import settings

from abstract.utils import generate_snowflake_id
from django.utils.translation import gettext as _
import datetime
from abstract.utils.audit_functions import perform_audit, Verb


class TenantMixin(auto_prefetch.Model):
    class Meta(auto_prefetch.Model.Meta):
        abstract = True

    tenant_id = 'ten_id'

    ten = TenantForeignKey('abstract.Tenant', null=True, editable=False,
                           on_delete=models.CASCADE, related_name='%(class)s_tenant')


class IntEntityManager(TenantManagerMixin, auto_prefetch.Manager):
    pass

class AuditMixin(auto_prefetch.Model):
    class Meta(auto_prefetch.Model.Meta):
        abstract = True

    def get_object_display(self, object_pk, model_cls):
        try:
            obj = model_cls.objects.get(pk=object_pk) if type(object_pk) == int else object_pk
            return str(obj)
        except Exception:
            if settings.DEBUG:
                raise
            return ""
    
    def format_changed_value(self, field, iv):
        if field.is_relation:
            return self.get_object_display(iv, field.related_model)
        if isinstance(iv, datetime.datetime):
            return iv.strftime(f"%Y-%m-%d %H:%M:%S")
        elif isinstance(iv, datetime.date):
            return iv.strftime(f"%Y-%m-%d")
        elif isinstance(iv, datetime.time):
            return iv.strftime(f"%H:%M:%S")
        elif isinstance(iv, datetime.timedelta):
            return str(iv)
        elif isinstance(iv, bool):
            # Abstract boolean formatter (replace as needed)
            return "✔️" if iv else "✖️"
        return iv

    def extract_update_message(self, deleted=None):
        message = {}
        fields = self._meta.fields
        serial = self.initial_value('serial') if 'serial' in [f.name for f in fields] else ""
        model_verbose_name = self._meta.verbose_name
        identifier = f"({serial})" if serial else self.id

        def get_initial_value(field):
            iv = self.initial_value(field.name)
            field_name = field.related_model._meta.verbose_name if field.is_relation else field.verbose_name
            return field_name, self.format_changed_value(field, iv)

        if not deleted:
            for field in fields:
                if field.name == 'password':
                    continue
                if self.has_changed(field.name):
                    field_name, initial_value = get_initial_value(field)
                    new_value = getattr(self, field.name, "")
                    new_value = self.format_changed_value(field, new_value)
                    message[str(field_name)] = format_html(
                        '<span class="fw-bold">({})</span> => <span class="fw-bold">({})</span>', initial_value, new_value)
            if self.has_changed('password'):
                message[str(field.verbose_name)] = _('تم التغيير بنجاح.')
        else:
            for field in fields:
                if field.name not in self.excluded_fields:
                    field_name, initial_value = get_initial_value(field)
                    if initial_value:
                        message[str(field_name)] = format_html('<span class="fw-bold">{}</span>', initial_value)
            message[_('العنصر المحذوف')] = f"{self._meta.verbose_name} ({self.id})"

        return message

    def extract_create_message(self):
        message = {}
        serial = getattr(self, 'serial', '')
        identifier = f"({serial})" if serial else self.id
        model_verbose_name = self._meta.verbose_name

        message[_('المتغير')] = f"{model_verbose_name} {identifier}"
        return message

    @hook(AFTER_CREATE)
    def create_audit(self):
        self.perform_create_audit(self.extract_create_message())

    @hook(BEFORE_UPDATE)
    def update_audit(self):
        deleted = self.has_changed('is_canceled')
        if deleted or any(self.has_changed(field.name) for field in self._meta.fields):
            self.perform_update_audit(self.extract_update_message(), deleted=deleted)

    @hook(BEFORE_DELETE)
    def delete_audit(self):
        self.perform_delete_audit(self.extract_update_message(deleted=True), deleted=True)

    def perform_create_audit(self, message):
        perform_audit(message=message, verb=Verb.CREATE.value, target=self)

    def perform_update_audit(self, message, deleted):
        verb = Verb.UPDATE.value if not deleted else Verb.DELETE.value
        perform_audit(message=message, verb=verb, target=self, deleted=deleted)

    def perform_delete_audit(self, message, deleted):
        perform_audit(message=message, verb=Verb.DELETE.value, target=self, deleted=deleted)



class IntEntity(TenantModelMixin, LifecycleModel, AuditMixin, TenantMixin):
    objects = IntEntityManager()

    CUSTOM_FIELDS_KEY = 'custom_fields'
    
    class Meta(auto_prefetch.Model.Meta):
        abstract = True
        ordering = ['-created_at']

    id = models.UUIDField(default=uuid.uuid4, primary_key=True, editable=False)
    uuid = models.UUIDField(_('ترميز قاعدة البيانات'), default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), editable=False, auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ آخر تحديث'), editable=False, auto_now=True)
    extra = models.JSONField(null=True, blank=True, default=dict)
    created_by = CurrentUserField(verbose_name=_('المنظم'), related_name="%(app_label)s_%(class)s_adder")
    updated_by = CurrentUserField(verbose_name=_('التعديل بواسطة'), on_update=True,
                                  related_name="%(app_label)s_%(class)s_updater")

    excluded_fields = [
        'uuid',
        'created_at',
        'updated_at',
        'extra',
        'created_by',
        'updated_by',
        'ten',
    ]

    def get_custom_fields_data(self):
        if not self.extra:
            self.extra = {}
        return self.extra.get(self.CUSTOM_FIELDS_KEY, {})

    def save_custom_fields_data(self, data):
        if not self.extra:
            self.extra = {}
        self.extra[self.CUSTOM_FIELDS_KEY] = data
