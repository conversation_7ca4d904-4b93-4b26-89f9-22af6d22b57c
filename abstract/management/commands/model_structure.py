import json
from django.core.management.base import BaseCommand
from django.apps import apps
from django.utils import translation
from django.utils.translation import gettext as _


class Command(BaseCommand):
    help = 'Returns the structure of a model in JSON format with language control'

    def add_arguments(self, parser):
        parser.add_argument('app_label', type=str, help='The app label of the model')
        parser.add_argument('model_name', type=str, help='The name of the model')
        parser.add_argument('--language', type=str, default='en', help='Language code for verbose names')

    def handle(self, *args, **kwargs):
        app_label = kwargs['app_label']
        model_name = kwargs['model_name']
        language_code = kwargs['language']
        model = apps.get_model(app_label, model_name)

        # Set the language
        translation.activate(language_code)

        fields = {}
        for field in model._meta.get_fields():
            if hasattr(field, 'verbose_name') and field.verbose_name:
                verbose_name = _(field.verbose_name)
            else:
                verbose_name = field.name  # Use the field's name if no verbose_name is available

            field_type = field.get_internal_type()
            fields[field.name] = {
                'verbose_name': verbose_name,
                'field_type': field_type
            }

        # Deactivate language
        translation.deactivate()

        self.stdout.write(self.style.SUCCESS(json.dumps(fields, indent=4)))