from django.contrib.auth.models import Permission
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = "Translate all permissions"

    def add_arguments(self, parser):
        pass

    def handle(self, *args, **options):
        permissions = Permission.objects.all()
        for permission in permissions:
            permission.name = (
                permission.name
                .replace('Can', 'صلاحية')
                .replace('view', ' عرض')
                .replace('add', ' إضافة')
                .replace('change', ' تعديل')
                .replace('delete', ' حذف')
            ).strip()
            permission.save()
