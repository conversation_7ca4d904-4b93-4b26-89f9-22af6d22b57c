import time
import uuid

# Snowflake epoch timestamp in milliseconds (2020-01-01)
EPOCH_START = 1577836800000

# Bit lengths of different parts of the ID
TIMESTAMP_BITS = 41
SEQUENCE_BITS = 12
NODE_ID_BITS = 11

# Maximum values of different parts of the ID
MAX_NODE_ID = (1 << NODE_ID_BITS) - 1
MAX_SEQUENCE = (1 << SEQUENCE_BITS) - 1

# Initialize variables
last_timestamp = -1
sequence = 0
node_id = 0


def get_node_id():
    mac = uuid.getnode()
    return (mac >> 6) & MAX_NODE_ID


def generate_snowflake_id():
    global last_timestamp, sequence, node_id

    # Get current timestamp in milliseconds
    timestamp = int(time.time() * 1000) - EPOCH_START

    # If the timestamp has not changed since last call, increment the sequence
    if timestamp == last_timestamp:
        sequence = (sequence + 1) & MAX_SEQUENCE
        if sequence == 0:
            # Sequence has rolled over, wait until next millisecond
            timestamp = wait_until_next_millisecond(last_timestamp)
    else:
        sequence = 0

    # If the timestamp has gone backwards, error out
    if timestamp < last_timestamp:
        raise Exception("Clock went backwards!")

    last_timestamp = timestamp

    # Get the node ID (can be a unique identifier for the machine or process)
    node_id = get_node_id()
    if node_id > MAX_NODE_ID:
        raise Exception("Node ID too large!")

    return (
            (timestamp << (NODE_ID_BITS + SEQUENCE_BITS))
            | (node_id << SEQUENCE_BITS)
            | sequence
    )


def wait_until_next_millisecond(last_timestamp):
    timestamp = int(time.time() * 1000) - EPOCH_START
    while timestamp <= last_timestamp:
        timestamp = int(time.time() * 1000) - EPOCH_START
    return timestamp

