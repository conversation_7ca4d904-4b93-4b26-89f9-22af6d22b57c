import uuid


def generate_unique_number(base = 1_000_000_000_000_000_000):
    '''
        Generate a unique number based on the uuid and base
    '''
    # Convert UUID to integer
    uuid_int = uuid.uuid4().int
    # Convert to given base and return as string
    return str(uuid_int % base)

def generate_unique_string(length = 5):
    '''
        Generate a unique string based on the uuid and length
    '''
    return str(uuid.uuid4())[:length]

if __name__ == "__main__":
    print(generate_unique_number())
    print(generate_unique_string())
