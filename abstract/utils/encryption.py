import base64
import hashlib

from cryptography.fernet import Fernet


class BaseEncryptor:
    def encrypt(self, data: bytes) -> bytes:
        raise NotImplementedError

    def decrypt(self, data: bytes) -> bytes:
        raise NotImplementedError

    def build(self):
        raise NotImplementedError


class FernetEncryptor(BaseEncryptor):
    def __init__(self, key: str):
        self.plain_key = key or Fernet.generate_key().decode('utf-8')
        self.random_key = hashlib.sha256(bytes(self.plain_key, 'utf-8')).digest()
        self.key = base64.urlsafe_b64encode(self.random_key).decode('utf-8')

    def build(self) -> Fernet:
        return Fernet(self.key)

    def encrypt(self, data: bytes) -> bytes:
        return self.build().encrypt(data)

    def decrypt(self, data: bytes) -> bytes:
        return self.build().decrypt(data)
