import json
from typing import cast

from ninja.parser import Parser
from ninja.renderers import <PERSON><PERSON><PERSON><PERSON>
from ninja.responses import Ninja<PERSON><PERSON><PERSON>nco<PERSON>
from ninja.types import DictStrAny


class NinjaGenericRenderer(BaseRenderer):
    media_type = 'text/plain'

    def __init__(self, renderer_method=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.renderer_method = renderer_method

    def render(self, request, data: dict, *, response_status):
        if self.renderer_method:
            return self.renderer_method(request, data, response_status)

        return json.dumps(data, cls=NinjaJSONEncoder, **{})


class NinjaGenericParser(Parser):

    def __init__(self, parser_method=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.parser_method = parser_method

    def parse_body(self, request):
        if self.parser_method:
            return self.parser_method(request)

        return cast(DictStrAny, json.loads(request.body))
