from functools import wraps
from http import HTTPStatus

from django.contrib import messages
from django.contrib.auth.models import Permission
from django.shortcuts import redirect
from django.urls import reverse_lazy
from django.utils.translation import gettext as _

from abstract.schemas import AlertMessage


def user_passes_test(test_func, name=''):
    """
    Decorator for views that checks that the user passes the given test,
    redirecting to the log-in page if necessary. The test should be a callable
    that takes the user object and returns True if the user passes.
    """

    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if test_func(request.user):
                return view_func(request, *args, **kwargs)
            messages.warning(request, _('لا تملك الصلاحيات الكافية: {}'.format(name)))
            return redirect(request.META.get('HTTP_REFERER', reverse_lazy('home:dashboard')))

        return _wrapped_view

    return decorator


def custom_permission_required(perm):
    """
    Decorator for views that checks whether a user has a particular permission
    enabled, redirecting to the log-in page if necessary.
    If the raise_exception parameter is given the PermissionDenied exception
    is raised.
    """

    def check_perms(user):
        perms = (perm,) if isinstance(perm, str) else perm
        # First check if the user has the permission (even anon users)
        if user.has_perms(perms):
            return True
        return False

    permission = ''
    try:
        permission = Permission.objects.get(codename=perm.split('.')[1])
    except Exception as e:
        print(e, perm)
    return user_passes_test(check_perms, name=permission.name if permission else '')


def api_user_passes_test(test_func):
    """
    Decorator for views that checks that the user passes the given test,
    return an AlertMessage if not
    """

    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if test_func(request.user):
                return view_func(request, *args, **kwargs)
            return HTTPStatus.FORBIDDEN, AlertMessage(
                color="red",
                details=_('لا تملك الصلاحيات الكافية')
            )

        return _wrapped_view

    return decorator


def api_custom_permission_required(perm):
    """
    Decorator for views that checks whether a user has a particular permission
    enabled, return an AlertMessage if not
    """

    def check_perms(user):
        perms = (perm,) if isinstance(perm, str) else perm
        # First check if the user has the permission (even anon users)
        # if settings.DEBUG:
        #     return True
        if user.has_perms(perms):
            return True
        return False

    return api_user_passes_test(check_perms)
