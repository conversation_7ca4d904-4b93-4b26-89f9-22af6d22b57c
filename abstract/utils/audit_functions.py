import enum

from actstream import action
from django_currentuser.middleware import get_current_authenticated_user
from orjson import orjson
from django.utils.translation import gettext_lazy as _

from config.settings import DEBUG


class Verb(enum.Enum):
    CREATE = _('بإنشاء')
    UPDATE = _('بتعديل')
    DELETE = _('بحذف')


def perform_audit(message: dict, verb: Verb, target,
                  action_object=None, deleted=None):
    if action_object:
        verb_target = action_object._meta.verbose_name
    else:
        verb_target = target._meta.verbose_name

    try:
        action.send(
            get_current_authenticated_user(),
            verb='{} {} {}'.format(_('قام'), verb, verb_target),
            target=target,
            action_object=None if deleted else action_object,
            data=orjson.loads(orjson.dumps(message)),
            deleted=deleted,
        )
    except Exception as e:
        if DEBUG:
            raise Exception(str(e))
        pass
