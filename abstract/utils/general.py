import hashlib
import random
import string
import time
from datetime import datetime, timedelta

import requests
from cryptography.fernet import <PERSON><PERSON><PERSON>
from django.utils import timezone
from django.utils.crypto import get_random_string

ALLOWED_INT = '0123456789'


def create_random_key(size: int = 140) -> str:
    return ''.join(random.choices(string.ascii_letters + string.digits, k=size))


def create_random_encryption_key() -> bytes:
    return Fernet.generate_key()


def random_string_generator(size=10, chars=ALLOWED_INT) -> str:
    """
    Description:Generate random values based on the size and chars passed.\n
    """
    return ''.join(random.choice(chars) for _ in range(size))


def generate_random_code(length=10, digits_only: bool = True):
    allowed = string.ascii_uppercase + string.digits
    if digits_only:
        allowed = string.digits
    return get_random_string(length=length, allowed_chars=allowed)


def generate_md5_hashcode(key_word):
    keyword = f'{key_word}-{time.time()}'
    return hashlib.md5(keyword.encode('utf-8')).hexdigest()


def generate_datetime(min_year=1900, max_year=datetime.now().year):
    """Generate a datetime."""
    start = datetime(min_year, 1, 1, 00, 00, 00)
    years = max_year - min_year + 1
    end = start + timedelta(days=365 * years)
    return start + (end - start) * random.random()


def d_30():
    return timezone.now() + timezone.timedelta(days=30)


def d_60():
    return timezone.now() + timezone.timedelta(days=60)

