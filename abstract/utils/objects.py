import itertools

from django.contrib.admin.utils import NestedObjects
from django.db import DEFAULT_DB_ALIAS


def get_related_objects(obj, using=DEFAULT_DB_ALIAS):
    # This code is based on https://github.com/makinacorpus/django-safedelete
    collector = NestedObjects(using=using)
    collector.collect([obj])

    def flatten(elem):
        if isinstance(elem, list):
            return itertools.chain.from_iterable(map(flatten, elem))
        elif obj != elem:
            return (elem,)
        return ()

    return flatten(collector.nested())


def clone_object(obj, attrs=None, omit_checks=False):
    if attrs is None:
        attrs = {}

    # we start by building a "flat" clone
    clone = obj._meta.model.objects.get(pk=obj.pk)
    clone.pk = None
    if omit_checks:
        clone.save(omit_checks=omit_checks)
    else:
        clone.save(skip_hooks=True)

    # if caller specified some attributes to be overridden,
    # use them
    for key, value in attrs.items():
        setattr(clone, key, value)

    # save the partial clone to have a valid ID assigned
    if omit_checks:
        clone.save(omit_checks=omit_checks)
    else:
        clone.save(skip_hooks=True)

    # Scan field to further investigate relations
    fields = clone._meta.get_fields()
    for field in fields:

        # Manage M2M fields by replicating all related records
        # found on parent "obj" into "clone"
        if not field.auto_created and field.many_to_many:
            for row in getattr(obj, field.name).all():
                getattr(clone, field.name).add(row)

        # Manage 1-N and 1-1 relations by cloning child objects
        if field.auto_created and field.is_relation and not field.many_to_many:
            # provide "clone" object to replace "obj"
            # on remote field
            attrs = {
                field.remote_field.name: clone
            }
            children = field.related_model.objects.filter(**{field.remote_field.name: obj})
            for child in children:
                clone_object(child, attrs)
    return clone

