import snowflake
from django.db import models


class SnowflakeField(models.BigIntegerField):
    """
    A custom field that generates a unique snowflake ID as the default value.
    """

    def __init__(self, *args, **kwargs):
        # kwargs['max_length'] = 64
        kwargs['default'] = snowflake.Snowflake().generate().id
        super().__init__(*args, **kwargs)

    def db_type(self, connection):
        return 'char(64)'
