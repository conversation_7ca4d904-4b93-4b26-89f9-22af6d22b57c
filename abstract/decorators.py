from functools import wraps
from http import HTT<PERSON>tatus

from abstract.schemas import <PERSON>ertMessage
from django.utils.translation import gettext as _


def check_device_id(view_func):
    """
    Decorator to check if the device ID in the request header matches the user's device ID.
    """

    @wraps(view_func)
    def _wrapped_view_func(request, *args, **kwargs):
        device_id = request.headers.get('X-Device-Id')

        if not device_id:
            return HTTPStatus.UNAUTHORIZED, AlertMessage(
                color="red",
                details=_('الحساب مرتبط بجهاز آخر')
            )

        if request.user.is_authenticated and request.user.device_id != device_id:
            return HTTPStatus.UNAUTHORIZED, AlertMessage(
                color="red",
                details=_('هذا الحساب مسجل على جهاز آخر')
            )

        return view_func(request, *args, **kwargs)

    return _wrapped_view_func


def update_location_service(view_func):
    """
    Decorator to update the location service is enabled from the headers.
    """

    @wraps(view_func)
    def _wrapped_view_func(request, *args, **kwargs):

        location_service = request.headers.get('X-Device-Is-Location-Enabled')

        if location_service and (location_service == 'true' or location_service == 'True'):
            request.user.location_service = True
            request.user.save()
        else:
            request.user.location_service = False
            request.user.save()
        return view_func(request, *args, **kwargs)

    return _wrapped_view_func
