from django.contrib.auth import logout
from django_multitenant.utils import set_current_tenant, unset_current_tenant

class MultitenantMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if request.user and not request.user.is_anonymous:
            if not getattr(request.user, 'ten', None) and not request.user.is_superuser:
                print(
                    "Logging out because user doesnt have account and not a superuser"
                )
                logout(request)

            set_current_tenant(getattr(request.user, 'ten', None))

        response = self.get_response(request)

        # Unset tenant after request to avoid thread-local leaks
        unset_current_tenant()

        return response 