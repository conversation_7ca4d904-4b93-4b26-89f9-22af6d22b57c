import math
from typing import List

from django.db.models import Q

from abstract.schemas import ListControllerSchema




def list_view_controller(
    qs, params: ListControllerSchema, filters, search_fields: List[str]
):

    if filters:
        qs = filters.filter(qs)

    if params.search:
        query = None
        for field in search_fields:
            q = Q(**{f"{field}__icontains": params.search})
            query = q if query is None else query | q
        qs = qs.filter(query)
    
    total = qs.count()
    total_pages = math.ceil(total / params.page_size)
        
    if params.sort_by:
        qs = (
            qs.order_by(params.sort_by)
            if params.sort_order == "asc"
            else qs.order_by(f"-{params.sort_by}")
        )
        


    if params.page_index and params.page_size:
        qs = qs[
            (params.page_index - 1)
            * params.page_size : params.page_index
            * params.page_size
        ]



    return {
        "total": total,
        "total_pages": total_pages,
        "params": params,
        "filters": filters,
        "items": qs,
    }
