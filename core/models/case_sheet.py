from django.core.validators import MaxV<PERSON>ueValidator
from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django_lifecycle import hook, BEFORE_CREATE
from abstract.models import IntEntity
from .patient import Patient


class CaseSheet(IntEntity):
    """
    Case sheet model for tracking patient visits and dental charting sessions.
    Provides the foundation for the split view: odontogram left, timeline right.
    """
    
    patient = models.ForeignKey(
        Patient, 
        on_delete=models.CASCADE, 
        related_name='case_sheets',
        verbose_name=_('Patient')
    )
    
    case_number = models.CharField(
        _('Case Number'), 
        max_length=20, 
        help_text=_('Auto-generated case number')
    )
    
    visit_date = models.DateTimeField(_('Visit Date'))
    
    CASE_TYPE_CHOICES = [
        ('CONSULTATION', _('Consultation')),
        ('EMERGENCY', _('Emergency')),
        ('ROUTINE_CHECKUP', _('Routine Checkup')),
        ('CLEANING', _('Cleaning')),
        ('TREATMENT', _('Treatment')),
        ('FOLLOW_UP', _('Follow-up')),
        ('SURGERY', _('Surgery')),
        ('ORTHODONTICS', _('Orthodontics')),
    ]
    case_type = models.CharField(
        _('Case Type'), 
        max_length=20, 
        choices=CASE_TYPE_CHOICES, 
        default='CONSULTATION'
    )
    
    # Chief complaint and presenting concerns
    chief_complaint = models.TextField(_('Chief Complaint'), blank=True)
    history_of_present_illness = models.TextField(_('History of Present Illness'), blank=True)
    
    # Clinical examination
    clinical_notes = models.TextField(_('Clinical Notes'), blank=True)
    examination_findings = models.TextField(_('Examination Findings'), blank=True)
    
    # Vital signs and measurements
    blood_pressure_systolic = models.PositiveIntegerField(_('Blood Pressure (Systolic)'), null=True, blank=True)
    blood_pressure_diastolic = models.PositiveIntegerField(_('Blood Pressure (Diastolic)'), null=True, blank=True)
    pulse_rate = models.PositiveIntegerField(_('Pulse Rate'), null=True, blank=True)
    temperature = models.DecimalField(_('Temperature'), max_digits=4, decimal_places=1, null=True, blank=True)
    
    # Dental specific measurements
    plaque_index = models.DecimalField(_('Plaque Index'), max_digits=3, decimal_places=2, null=True, blank=True)
    gingival_index = models.DecimalField(_('Gingival Index'), max_digits=3, decimal_places=2, null=True, blank=True)
    bleeding_on_probing = models.BooleanField(_('Bleeding on Probing'), default=False)
        # Periodontal measurements
    probing_depth = models.PositiveIntegerField(
        _('Probing Depth MB (mm)'),
        null=True,
        blank=True,
        validators=[MaxValueValidator(15)],
        help_text=_('probing depth')
    )
    
    
    # Mobility
    MOBILITY_CHOICES = [
        (0, _('No Mobility')),
        (1, _('Slight Mobility (1mm)')),
        (2, _('Moderate Mobility (1-2mm)')),
        (3, _('Severe Mobility (>2mm or vertical)')),
    ]
    mobility = models.PositiveIntegerField(
        _('Mobility'),
        choices=MOBILITY_CHOICES,
        default=0
    )
    
    # Case status and workflow
    CASE_STATUS_CHOICES = [
        ('DRAFT', _('Draft')),
        ('IN_PROGRESS', _('In Progress')),
        ('COMPLETED', _('Completed')),
        ('CANCELLED', _('Cancelled')),
        ('REQUIRES_FOLLOWUP', _('Requires Follow-up')),
    ]
    status = models.CharField(
        _('Case Status'), 
        max_length=20, 
        choices=CASE_STATUS_CHOICES, 
        default='DRAFT'
    )
    
    # Timing information
    estimated_duration_minutes = models.PositiveIntegerField(_('Estimated Duration (Minutes)'), default=60)
    actual_duration_minutes = models.PositiveIntegerField(_('Actual Duration (Minutes)'), null=True, blank=True)
    
    # Provider information
    primary_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='primary_case_sheets',
        verbose_name=_('Primary Provider')
    )
    
    assisting_providers = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        blank=True,
        related_name='assisting_case_sheets',
        verbose_name=_('Assisting Providers')
    )
    
    # Treatment planning
    treatment_plan_presented = models.BooleanField(_('Treatment Plan Presented'), default=False)
    treatment_plan_accepted = models.BooleanField(_('Treatment Plan Accepted'), default=False)
    patient_signature_date = models.DateTimeField(_('Patient Signature Date'), null=True, blank=True)
    
    # Follow-up and recall
    next_appointment_recommended = models.BooleanField(_('Next Appointment Recommended'), default=False)
    recommended_followup_weeks = models.PositiveIntegerField(_('Recommended Follow-up (Weeks)'), null=True, blank=True)
    
    # Administrative
    insurance_claim_submitted = models.BooleanField(_('Insurance Claim Submitted'), default=False)
    insurance_claim_number = models.CharField(_('Insurance Claim Number'), max_length=100, blank=True)
    
    # Quality and compliance
    peer_review_required = models.BooleanField(_('Peer Review Required'), default=False)
    peer_review_completed = models.BooleanField(_('Peer Review Completed'), default=False)
    peer_reviewer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='peer_reviewed_cases',
        verbose_name=_('Peer Reviewer')
    )
    
    # Patient experience
    PAIN_LEVEL_CHOICES = [
        (0, _('No Pain')),
        (1, _('Mild Pain')),
        (2, _('Mild Pain')),
        (3, _('Moderate Pain')),
        (4, _('Moderate Pain')),
        (5, _('Moderate Pain')),
        (6, _('Severe Pain')),
        (7, _('Severe Pain')),
        (8, _('Very Severe Pain')),
        (9, _('Very Severe Pain')),
        (10, _('Worst Possible Pain')),
    ]
    pain_level_before = models.PositiveIntegerField(
        _('Pain Level Before Treatment'), 
        choices=PAIN_LEVEL_CHOICES, 
        null=True, 
        blank=True
    )
    pain_level_after = models.PositiveIntegerField(
        _('Pain Level After Treatment'), 
        choices=PAIN_LEVEL_CHOICES, 
        null=True, 
        blank=True
    )
    
    # Additional notes and observations
    patient_cooperation = models.TextField(_('Patient Cooperation Notes'), blank=True)
    complications = models.TextField(_('Complications'), blank=True)
    post_treatment_instructions = models.TextField(_('Post-treatment Instructions'), blank=True)
    
    class Meta(IntEntity.Meta):
        verbose_name = _('Case Sheet')
        verbose_name_plural = _('Case Sheets')
        ordering = ['-visit_date']
        default_permissions = {}
        indexes = [
            models.Index(fields=['patient', '-visit_date']),
            models.Index(fields=['case_number']),
            models.Index(fields=['status']),
            models.Index(fields=['case_type']),
            models.Index(fields=['visit_date']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['case_number', 'ten'], 
                name='unique_case_number_per_tenant'
            )
        ]
    
    def __str__(self):
        return f"Case {self.case_number} - {self.patient.full_name} ({self.visit_date.strftime('%Y-%m-%d')})"
    
    @property
    def duration_display(self):
        """Return formatted duration string"""
        if self.actual_duration_minutes:
            hours = self.actual_duration_minutes // 60
            minutes = self.actual_duration_minutes % 60
            if hours > 0:
                return f"{hours}h {minutes}m"
            return f"{minutes}m"
        return f"{self.estimated_duration_minutes}m (estimated)"
    
    @property
    def is_overdue(self):
        """Check if case is overdue for completion"""
        if self.status in ['DRAFT', 'IN_PROGRESS']:
            from datetime import datetime, timedelta
            from django.utils import timezone
            overdue_threshold = timezone.now() - timedelta(hours=24)
            return self.visit_date < overdue_threshold
        return False
    
    @property
    def pain_improvement(self):
        """Calculate pain level improvement"""
        if self.pain_level_before is not None and self.pain_level_after is not None:
            return self.pain_level_before - self.pain_level_after
        return None
    
    @hook(BEFORE_CREATE)
    def generate_case_number(self):
        """Generate case number if not provided"""
        if not self.case_number:
            # Generate a unique case number
            from datetime import datetime
            import random
            import string
            
            date_str = datetime.now().strftime('%Y%m%d')
            while True:
                random_suffix = ''.join(random.choices(string.digits, k=3))
                case_number = f"C{date_str}{random_suffix}"
                if not CaseSheet.objects.filter(case_number=case_number).exists():
                    self.case_number = case_number
                    break 