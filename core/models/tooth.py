from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from django_lifecycle import hook, BEFORE_SAVE
from abstract.models import IntEntity
from .patient import Patient


class Tooth(IntEntity):
    """
    Tooth model implementing FDI two-digit notation system.
    Supports both adult (permanent) and child (deciduous) dentition.
    
    FDI System:
    - Adults: Quadrants 1-4 (11-18, 21-28, 31-38, 41-48)
    - Children: Quadrants 5-8 (51-55, 61-65, 71-75, 81-85)
    """
    
    patient = models.ForeignKey(
        Patient,
        on_delete=models.CASCADE,
        related_name='teeth',
        verbose_name=_('Patient')
    )
    
    # FDI notation
    fdi_number = models.PositiveIntegerField(
        _('FDI Number'),
        validators=[MinValueValidator(11), MaxValueValidator(85)],
        help_text=_('FDI two-digit tooth notation (11-48 for adults, 51-85 for children)')
    )
    
    TOOTH_TYPE_CHOICES = [
        ('PERMANENT', _('Permanent')),
        ('DECIDUOUS', _('Deciduous/Primary')),
    ]
    tooth_type = models.CharField(
        _('Tooth Type'),
        max_length=20,
        choices=TOOTH_TYPE_CHOICES,
        help_text=_('Permanent (adult) or deciduous (child) tooth')
    )
    
    # Anatomical classification
    TOOTH_CLASS_CHOICES = [
        ('INCISOR', _('Incisor')),
        ('CANINE', _('Canine')),
        ('PREMOLAR', _('Premolar')),
        ('MOLAR', _('Molar')),
    ]
    tooth_class = models.CharField(
        _('Tooth Class'),
        max_length=20,
        choices=TOOTH_CLASS_CHOICES
    )
    
    # Position information
    QUADRANT_CHOICES = [
        (1, _('Upper Right (1)')),
        (2, _('Upper Left (2)')),
        (3, _('Lower Left (3)')),
        (4, _('Lower Right (4)')),
    ]
    quadrant = models.PositiveIntegerField(
        _('Quadrant'),
        choices=QUADRANT_CHOICES
    )
    
    position_in_quadrant = models.PositiveIntegerField(
        _('Position in Quadrant'),
        validators=[MinValueValidator(1), MaxValueValidator(8)]
    )
    
    # Tooth status
    TOOTH_STATUS_CHOICES = [
        ('PRESENT', _('Present')),
        ('MISSING', _('Missing')),
        ('EXTRACTED', _('Extracted')),
        ('WEAR', _('Wear')),
        ('DISCOLORATION', _('Discoloration')),
        ('ATTRITION', _('Attrition')),
        ('IMPACTED', _('Impacted')),
        ('CONGENITALLY_MISSING', _('Congenitally Missing')),
        ('SUPERNUMERARY', _('Supernumerary')),
        ('ROOT_FRAGMENT', _('Root Fragment')),
        ('IMPLANT', _('Implant')),
        ('BRIDGE_ABUTMENT', _('Bridge Abutment')),
        ('CROWN', _('Crown')),
    ]
    status = models.CharField(
        _('Tooth Status'),
        max_length=30,
        choices=TOOTH_STATUS_CHOICES,
        default='PRESENT'
    )
    
    

    
    # Additional clinical information
    is_vital = models.BooleanField(_('Vital'), default=True)
    pulp_test_result = models.CharField(
        _('Pulp Test Result'),
        max_length=100,
        blank=True,
        help_text=_('Results of vitality testing')
    )
    
    percussion_test = models.CharField(
        _('Percussion Test'),
        max_length=20,
        choices=[
            ('NEGATIVE', _('Negative')),
            ('POSITIVE', _('Positive')),
            ('NOT_TESTED', _('Not Tested')),
        ],
        default='NOT_TESTED'
    )
    
    palpation_test = models.CharField(
        _('Palpation Test'),
        max_length=20,
        choices=[
            ('NEGATIVE', _('Negative')),
            ('POSITIVE', _('Positive')),
            ('NOT_TESTED', _('Not Tested')),
        ],
        default='NOT_TESTED'
    )
    
    # Treatment planning
    treatment_priority = models.CharField(
        _('Treatment Priority'),
        max_length=20,
        choices=[
            ('EMERGENCY', _('Emergency')),
            ('URGENT', _('Urgent')),
            ('ROUTINE', _('Routine')),
            ('ELECTIVE', _('Elective')),
            ('NONE', _('No Treatment Needed')),
        ],
        default='NONE'
    )
    
    # Notes and observations
    clinical_notes = models.TextField(_('Clinical Notes'), blank=True)
    patient_symptoms = models.TextField(_('Patient Symptoms'), blank=True)
    
    # Dates
    last_examined_date = models.DateField(_('Last Examined'), null=True, blank=True)
    last_treatment_date = models.DateField(_('Last Treatment'), null=True, blank=True)
    
    class Meta(IntEntity.Meta):
        verbose_name = _('Tooth')
        verbose_name_plural = _('Teeth')
        ordering = ['fdi_number']
        default_permissions = {}
        indexes = [
            models.Index(fields=['patient', 'fdi_number']),
            models.Index(fields=['fdi_number']),
            models.Index(fields=['status']),
            models.Index(fields=['treatment_priority']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['patient', 'fdi_number'],
                name='unique_tooth_per_patient'
            )
        ]
    
    def __str__(self):
        return f"Tooth {self.fdi_number} - {self.patient.full_name}"
    
    @property
    def tooth_name(self):
        """Return anatomical name of the tooth"""
        tooth_names = {
            # Permanent teeth
            11: 'Upper Right Central Incisor', 12: 'Upper Right Lateral Incisor',
            13: 'Upper Right Canine', 14: 'Upper Right First Premolar',
            15: 'Upper Right Second Premolar', 16: 'Upper Right First Molar',
            17: 'Upper Right Second Molar', 18: 'Upper Right Third Molar',
            
            21: 'Upper Left Central Incisor', 22: 'Upper Left Lateral Incisor',
            23: 'Upper Left Canine', 24: 'Upper Left First Premolar',
            25: 'Upper Left Second Premolar', 26: 'Upper Left First Molar',
            27: 'Upper Left Second Molar', 28: 'Upper Left Third Molar',
            
            31: 'Lower Left Central Incisor', 32: 'Lower Left Lateral Incisor',
            33: 'Lower Left Canine', 34: 'Lower Left First Premolar',
            35: 'Lower Left Second Premolar', 36: 'Lower Left First Molar',
            37: 'Lower Left Second Molar', 38: 'Lower Left Third Molar',
            
            41: 'Lower Right Central Incisor', 42: 'Lower Right Lateral Incisor',
            43: 'Lower Right Canine', 44: 'Lower Right First Premolar',
            45: 'Lower Right Second Premolar', 46: 'Lower Right First Molar',
            47: 'Lower Right Second Molar', 48: 'Lower Right Third Molar',
            
            # Deciduous teeth
            51: 'Upper Right Central Incisor (Primary)', 52: 'Upper Right Lateral Incisor (Primary)',
            53: 'Upper Right Canine (Primary)', 54: 'Upper Right First Molar (Primary)',
            55: 'Upper Right Second Molar (Primary)',
            
            61: 'Upper Left Central Incisor (Primary)', 62: 'Upper Left Lateral Incisor (Primary)',
            63: 'Upper Left Canine (Primary)', 64: 'Upper Left First Molar (Primary)',
            65: 'Upper Left Second Molar (Primary)',
            
            71: 'Lower Left Central Incisor (Primary)', 72: 'Lower Left Lateral Incisor (Primary)',
            73: 'Lower Left Canine (Primary)', 74: 'Lower Left First Molar (Primary)',
            75: 'Lower Left Second Molar (Primary)',
            
            81: 'Lower Right Central Incisor (Primary)', 82: 'Lower Right Lateral Incisor (Primary)',
            83: 'Lower Right Canine (Primary)', 84: 'Lower Right First Molar (Primary)',
            85: 'Lower Right Second Molar (Primary)',
        }
        return tooth_names.get(self.fdi_number, f'Tooth {self.fdi_number}')
    
    @property
    def is_anterior(self):
        """Check if tooth is anterior (incisors and canines)"""
        position = self.position_in_quadrant
        return position <= 3
    
    @property
    def is_posterior(self):
        """Check if tooth is posterior (premolars and molars)"""
        return not self.is_anterior
    
    @property
    def max_probing_depth(self):
        """Return maximum probing depth measurement"""
        depths = [
            self.probing_depth_mb, self.probing_depth_b, self.probing_depth_db,
            self.probing_depth_ml, self.probing_depth_l, self.probing_depth_dl
        ]
        valid_depths = [d for d in depths if d is not None]
        return max(valid_depths) if valid_depths else None
    
    @property
    def has_periodontal_disease(self):
        """Check if tooth shows signs of periodontal disease"""
        max_depth = self.max_probing_depth
        return max_depth and max_depth >= 4
    
    @property
    def needs_treatment(self):
        """Check if tooth requires treatment"""
        return self.treatment_priority in ['EMERGENCY', 'URGENT', 'ROUTINE']
    
    @hook(BEFORE_SAVE)
    def populate_tooth_data_from_fdi(self):
        """Auto-populate quadrant and position from FDI number"""
        if self.fdi_number:
            self.quadrant = int(str(self.fdi_number)[0])
            self.position_in_quadrant = int(str(self.fdi_number)[1])
            
            # Determine tooth type
            if self.quadrant <= 4:
                self.tooth_type = 'PERMANENT'
            else:
                self.tooth_type = 'DECIDUOUS'
            
            # Determine tooth class based on position
            if self.position_in_quadrant <= 2:
                self.tooth_class = 'INCISOR'
            elif self.position_in_quadrant == 3:
                self.tooth_class = 'CANINE'
            elif self.position_in_quadrant in [4, 5] and self.tooth_type == 'PERMANENT':
                self.tooth_class = 'PREMOLAR'
            else:
                self.tooth_class = 'MOLAR' 