from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from django_lifecycle import BEFORE_CREATE, BEFORE_SAVE, hook
from abstract.models import IntEntity
from .patient import Patient
from .treatment_plan import TreatmentPlan, TreatmentPlanItem


class Appointment(IntEntity):
    """
    Appointment model for scheduling patient visits.
    Supports recall appointments, treatment planning integration, and workflow management.
    """
    
    patient = models.ForeignKey(
        Patient,
        on_delete=models.CASCADE,
        related_name='appointments',
        verbose_name=_('Patient')
    )
    
    appointment_number = models.CharField(
        _('Appointment Number'),
        max_length=20,
        help_text=_('Auto-generated appointment number')
    )
    
    # Schedule details
    appointment_date = models.DateTimeField(_('Appointment Date'))
    
    duration_minutes = models.PositiveIntegerField(
        _('Duration (Minutes)'),
        default=60,
        validators=[MinValueValidator(15), MaxValueValidator(480)]
    )
    
    end_time = models.DateTimeField(
        _('End Time'),
        null=True,
        blank=True,
        help_text=_('Auto-calculated from start time and duration')
    )
    
    # Appointment type and purpose
    APPOINTMENT_TYPE_CHOICES = [
        ('CONSULTATION', _('Consultation')),
        ('ROUTINE_CHECKUP', _('Routine Checkup')),
        ('CLEANING', _('Cleaning')),
        ('EMERGENCY', _('Emergency')),
        ('TREATMENT', _('Treatment')),
        ('FOLLOW_UP', _('Follow-up')),
        ('SURGERY', _('Surgery')),
        ('ORTHODONTIC', _('Orthodontic')),
        ('RECALL', _('Recall')),
        ('NEW_PATIENT', _('New Patient')),
        ('TELEHEALTH', _('Telehealth')),
        ('BLOCK_TIME', _('Block Time')),
    ]
    appointment_type = models.CharField(
        _('Appointment Type'),
        max_length=20,
        choices=APPOINTMENT_TYPE_CHOICES,
        default='CONSULTATION'
    )
    
    # Provider assignment
    primary_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='primary_appointments',
        verbose_name=_('Primary Provider')
    )
    
    assisting_providers = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        blank=True,
        related_name='assisting_appointments',
        verbose_name=_('Assisting Providers')
    )
    
    # Appointment status
    APPOINTMENT_STATUS_CHOICES = [
        ('SCHEDULED', _('Scheduled')),
        ('CONFIRMED', _('Confirmed')),
        ('CHECKED_IN', _('Checked In')),
        ('IN_PROGRESS', _('In Progress')),
        ('COMPLETED', _('Completed')),
        ('NO_SHOW', _('No Show')),
        ('CANCELLED', _('Cancelled')),
        ('RESCHEDULED', _('Rescheduled')),
        ('LATE_CANCELLATION', _('Late Cancellation')),
    ]
    status = models.CharField(
        _('Appointment Status'),
        max_length=30,
        choices=APPOINTMENT_STATUS_CHOICES,
        default='SCHEDULED'
    )
    
    # Treatment plan integration
    treatment_plan = models.ForeignKey(
        TreatmentPlan,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='appointments',
        verbose_name=_('Treatment Plan')
    )
    
    treatment_plan_items = models.ManyToManyField(
        TreatmentPlanItem,
        blank=True,
        related_name='appointments',
        verbose_name=_('Treatment Plan Items'),
        help_text=_('Specific treatment items to be performed')
    )
    
    # Appointment details
    chief_complaint = models.TextField(
        _('Chief Complaint'),
        blank=True,
        help_text=_('Patient\'s primary concern or reason for visit')
    )
    
    procedure_codes = models.CharField(
        _('Procedure Codes'),
        max_length=200,
        blank=True,
        help_text=_('Planned procedure codes (comma-separated)')
    )
    
    clinical_notes = models.TextField(_('Clinical Notes'), blank=True)
    
    # Priority and urgency
    PRIORITY_CHOICES = [
        ('EMERGENCY', _('Emergency')),
        ('URGENT', _('Urgent')),
        ('ROUTINE', _('Routine')),
        ('ELECTIVE', _('Elective')),
    ]
    priority = models.CharField(
        _('Priority'),
        max_length=20,
        choices=PRIORITY_CHOICES,
        default='ROUTINE'
    )
    
    # Recall functionality
    is_recall_appointment = models.BooleanField(_('Recall Appointment'), default=False)
    recall_type = models.CharField(
        _('Recall Type'),
        max_length=30,
        choices=[
            ('ROUTINE_CLEANING', _('Routine Cleaning')),
            ('PERIODONTAL_MAINTENANCE', _('Periodontal Maintenance')),
            ('ORTHODONTIC_ADJUSTMENT', _('Orthodontic Adjustment')),
            ('POST_TREATMENT', _('Post-treatment Check')),
            ('FLUORIDE_TREATMENT', _('Fluoride Treatment')),
            ('ORAL_CANCER_SCREENING', _('Oral Cancer Screening')),
            ('CUSTOM', _('Custom Recall')),
        ],
        blank=True
    )
    
    recall_interval_months = models.PositiveIntegerField(
        _('Recall Interval (Months)'),
        null=True,
        blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(24)]
    )
    
    # Room and resource assignment
    treatment_room = models.CharField(
        _('Treatment Room'),
        max_length=50,
        blank=True,
        help_text=_('Assigned treatment room or operatory')
    )
    
    special_equipment_needed = models.TextField(
        _('Special Equipment Needed'),
        blank=True
    )
    
    # Patient communication and confirmation
    confirmation_sent = models.BooleanField(_('Confirmation Sent'), default=False)
    confirmation_method = models.CharField(
        _('Confirmation Method'),
        max_length=20,
        choices=[
            ('EMAIL', _('Email')),
            ('SMS', _('SMS')),
            ('PHONE', _('Phone Call')),
            ('WHATSAPP', _('WhatsApp')),
            ('PATIENT_PORTAL', _('Patient Portal')),
        ],
        blank=True
    )
    confirmation_date = models.DateTimeField(_('Confirmation Date'), null=True, blank=True)
    
    reminder_sent = models.BooleanField(_('Reminder Sent'), default=False)
    reminder_date = models.DateTimeField(_('Reminder Date'), null=True, blank=True)
    
    # Check-in and timing
    check_in_time = models.DateTimeField(_('Check-in Time'), null=True, blank=True)
    actual_start_time = models.DateTimeField(_('Actual Start Time'), null=True, blank=True)
    actual_end_time = models.DateTimeField(_('Actual End Time'), null=True, blank=True)
    
    # Financial information
    estimated_fee = models.DecimalField(
        _('Estimated Fee'),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    
    insurance_pre_authorization = models.CharField(
        _('Insurance Pre-authorization'),
        max_length=100,
        blank=True
    )
    
    copay_collected = models.DecimalField(
        _('Copay Collected'),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    
    # Special requirements and notes
    special_instructions = models.TextField(
        _('Special Instructions'),
        blank=True,
        help_text=_('Special instructions for providers or staff')
    )
    
    pre_medication_required = models.BooleanField(_('Pre-medication Required'), default=False)
    pre_medication_notes = models.TextField(_('Pre-medication Notes'), blank=True)
    
    # Accessibility and accommodations
    wheelchair_accessible = models.BooleanField(_('Wheelchair Accessible'), default=False)
    interpreter_needed = models.BooleanField(_('Interpreter Needed'), default=False)
    interpreter_language = models.CharField(
        _('Interpreter Language'),
        max_length=50,
        blank=True
    )
    
    # Cancellation and rescheduling
    cancellation_reason = models.TextField(_('Cancellation Reason'), blank=True)
    cancelled_by = models.CharField(
        _('Cancelled By'),
        max_length=20,
        choices=[
            ('PATIENT', _('Patient')),
            ('PROVIDER', _('Provider')),
            ('STAFF', _('Staff')),
            ('SYSTEM', _('System')),
        ],
        blank=True
    )
    cancellation_date = models.DateTimeField(_('Cancellation Date'), null=True, blank=True)
    
    rescheduled_from = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='rescheduled_appointments',
        verbose_name=_('Rescheduled From')
    )
    
    # No-show handling
    no_show_fee = models.DecimalField(
        _('No Show Fee'),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    
    no_show_notes = models.TextField(_('No Show Notes'), blank=True)
    
    # Follow-up scheduling
    follow_up_needed = models.BooleanField(_('Follow-up Needed'), default=False)
    follow_up_interval_weeks = models.PositiveIntegerField(
        _('Follow-up Interval (Weeks)'),
        null=True,
        blank=True
    )
    
    # Quality and satisfaction
    patient_satisfaction_score = models.PositiveIntegerField(
        _('Patient Satisfaction Score'),
        null=True,
        blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text=_('1-5 scale satisfaction rating')
    )
    
    patient_feedback = models.TextField(_('Patient Feedback'), blank=True)
    
    # Administrative
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_appointments',
        verbose_name=_('Created By')
    )
    
    modified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='modified_appointments',
        verbose_name=_('Last Modified By')
    )
    
    class Meta(IntEntity.Meta):
        verbose_name = _('Appointment')
        verbose_name_plural = _('Appointments')
        ordering = ['appointment_date']
        default_permissions = {}
        indexes = [
            models.Index(fields=['patient', 'appointment_date']),
            models.Index(fields=['appointment_date']),
            models.Index(fields=['primary_provider', 'appointment_date']),
            models.Index(fields=['status']),
            models.Index(fields=['appointment_type']),
            models.Index(fields=['is_recall_appointment']),
            models.Index(fields=['treatment_room', 'appointment_date']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['appointment_number', 'ten'],
                name='unique_appointment_number_per_tenant'
            )
        ]
    
    def __str__(self):
        return f"Appointment {self.appointment_number} - {self.patient.full_name} ({self.appointment_date.strftime('%Y-%m-%d %H:%M')})"
    
    @property
    def duration_display(self):
        """Return formatted duration string"""
        hours = self.duration_minutes // 60
        minutes = self.duration_minutes % 60
        if hours > 0:
            return f"{hours}h {minutes}m"
        return f"{minutes}m"
    
    @property
    def is_today(self):
        """Check if appointment is today"""
        from datetime import date
        return self.appointment_date.date() == date.today()
    
    @property
    def is_overdue(self):
        """Check if appointment is overdue for completion"""
        if self.status in ['SCHEDULED', 'CONFIRMED', 'CHECKED_IN', 'IN_PROGRESS']:
            from django.utils import timezone
            return timezone.now() > self.appointment_date
        return False
    
    @property
    def actual_duration_minutes(self):
        """Calculate actual appointment duration"""
        if self.actual_start_time and self.actual_end_time:
            delta = self.actual_end_time - self.actual_start_time
            return int(delta.total_seconds() / 60)
        return None
    
    @property
    def wait_time_minutes(self):
        """Calculate patient wait time"""
        if self.check_in_time and self.actual_start_time:
            delta = self.actual_start_time - self.check_in_time
            return int(delta.total_seconds() / 60)
        return None
    
    @property
    def is_late_cancellation(self):
        """Check if cancellation was made within 24 hours"""
        if self.status in ['CANCELLED', 'LATE_CANCELLATION'] and self.cancellation_date:
            from datetime import timedelta
            notice_hours = (self.appointment_date - self.cancellation_date).total_seconds() / 3600
            return notice_hours < 24
        return False
    
    @property
    def next_recall_date(self):
        """Calculate next recall date"""
        if self.is_recall_appointment and self.recall_interval_months:
            from dateutil.relativedelta import relativedelta
            return self.appointment_date.date() + relativedelta(months=self.recall_interval_months)
        return None
    
    @property
    def status_color(self):
        """Return color code for status display"""
        color_map = {
            'SCHEDULED': 'blue',
            'CONFIRMED': 'green',
            'CHECKED_IN': 'orange',
            'IN_PROGRESS': 'purple',
            'COMPLETED': 'green',
            'NO_SHOW': 'red',
            'CANCELLED': 'gray',
            'RESCHEDULED': 'yellow',
            'LATE_CANCELLATION': 'red',
        }
        return color_map.get(self.status, 'gray')
    
    def can_be_cancelled(self):
        """Check if appointment can be cancelled"""
        return self.status in ['SCHEDULED', 'CONFIRMED']
    
    def can_be_rescheduled(self):
        """Check if appointment can be rescheduled"""
        return self.status in ['SCHEDULED', 'CONFIRMED']
    
    def create_next_recall(self):
        """Create next recall appointment"""
        if self.is_recall_appointment and self.recall_interval_months:
            next_date = self.next_recall_date
            if next_date:
                # Create new recall appointment
                next_appointment = Appointment.objects.create(
                    patient=self.patient,
                    appointment_date=next_date,
                    duration_minutes=self.duration_minutes,
                    appointment_type=self.appointment_type,
                    primary_provider=self.primary_provider,
                    is_recall_appointment=True,
                    recall_type=self.recall_type,
                    recall_interval_months=self.recall_interval_months,
                    status='SCHEDULED'
                )
                return next_appointment
        return None
    
    @hook(BEFORE_CREATE)
    def generate_appointment_number(self):
        """Generate appointment number if not provided"""
        if not self.appointment_number:
            from datetime import datetime
            import random
            import string
            
            date_str = datetime.now().strftime('%Y%m%d')
            while True:
                random_suffix = ''.join(random.choices(string.digits, k=3))
                appointment_number = f"APT{date_str}{random_suffix}"
                if not Appointment.objects.filter(appointment_number=appointment_number).exists():
                    self.appointment_number = appointment_number
                    break
    
    @hook(BEFORE_SAVE)
    def calculate_end_time(self):
        """Auto-calculate end time"""
        if self.appointment_date and self.duration_minutes:
            from datetime import timedelta
            self.end_time = self.appointment_date + timedelta(minutes=self.duration_minutes) 