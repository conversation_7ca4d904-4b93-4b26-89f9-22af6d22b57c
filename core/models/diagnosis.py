from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django_lifecycle import hook, BEFORE_SAVE
from abstract.models import IntEntity
from .patient import Patient
from .case_sheet import CaseSheet


class Diagnosis(IntEntity):
    """
    Diagnosis model implementing ICD-11 dental codes with prognosis scale.
    Supports pre-loaded drop-downs of 250+ ICD-11 dental codes and 
    prognosis scale aligned with periodontal literature.
    """
    
    patient = models.ForeignKey(
        Patient,
        on_delete=models.CASCADE,
        related_name='diagnoses',
        verbose_name=_('Patient')
    )
    
    case_sheet = models.ForeignKey(
        CaseSheet,
        on_delete=models.CASCADE,
        related_name='diagnoses',
        verbose_name=_('Case Sheet'),
        null=True,
        blank=True
    )
    
    diagnosis_date = models.DateTimeField(_('Diagnosis Date'))
    
    # Primary diagnosis classification
    DIAGNOSIS_CATEGORY_CHOICES = [
        ('CARIES', _('Dental Caries')),
        ('PERIODONTAL', _('Periodontal Disease')),
        ('ENDODONTIC', _('Endodontic')),
        ('ORAL_PATHOLOGY', _('Oral Pathology')),
        ('ORTHODONTIC', _('Orthodontic')),
        ('ORAL_SURGERY', _('Oral Surgery')),
        ('PROSTHODONTIC', _('Prosthodontic')),
        ('PREVENTIVE', _('Preventive')),
        ('TMJ', _('TMJ Disorders')),
        ('TRAUMA', _('Trauma')),
        ('CONGENITAL', _('Congenital Anomalies')),
        ('OTHER', _('Other')),
    ]
    diagnosis_category = models.CharField(
        _('Diagnosis Category'),
        max_length=20,
        choices=DIAGNOSIS_CATEGORY_CHOICES
    )
    
    # Prognosis scale aligned with periodontal literature
    PROGNOSIS_CHOICES = [
        ('GOOD', _('Good')),
        ('FAIR', _('Fair')),
        ('POOR', _('Poor')),
        ('HOPELESS', _('Hopeless')),
    ]
    prognosis = models.CharField(
        _('Prognosis'),
        max_length=20,
        choices=PROGNOSIS_CHOICES,
        help_text=_('Prognosis scale aligned with periodontal literature')
    )
    
    # Clinical details
    clinical_description = models.TextField(
        _('Clinical Description'),
        help_text=_('Detailed clinical description of the diagnosis')
    )
    
    etiology = models.TextField(
        _('Etiology'),
        blank=True,
        help_text=_('Suspected cause or contributing factors')
    )
    
    # Severity and staging
    SEVERITY_CHOICES = [
        ('MILD', _('Mild')),
        ('MODERATE', _('Moderate')),
        ('SEVERE', _('Severe')),
        ('EXTENSIVE', _('Extensive')),
    ]
    severity = models.CharField(
        _('Severity'),
        max_length=20,
        choices=SEVERITY_CHOICES,
        blank=True
    )
    
    # Periodontal specific staging (for periodontal diagnoses)
    PERIODONTAL_STAGE_CHOICES = [
        ('STAGE_I', _('Stage I - Initial')),
        ('STAGE_II', _('Stage II - Moderate')),
        ('STAGE_III', _('Stage III - Severe with potential for additional tooth loss')),
        ('STAGE_IV', _('Stage IV - Severe with extensive tooth loss')),
    ]
    periodontal_stage = models.CharField(
        _('Periodontal Stage'),
        max_length=20,
        choices=PERIODONTAL_STAGE_CHOICES,
        blank=True,
        help_text=_('AAP 2017 Classification')
    )
    
    PERIODONTAL_GRADE_CHOICES = [
        ('GRADE_A', _('Grade A - Slow rate of progression')),
        ('GRADE_B', _('Grade B - Moderate rate of progression')),
        ('GRADE_C', _('Grade C - Rapid rate of progression')),
    ]
    periodontal_grade = models.CharField(
        _('Periodontal Grade'),
        max_length=20,
        choices=PERIODONTAL_GRADE_CHOICES,
        blank=True,
        help_text=_('AAP 2017 Classification')
    )
    
    # Associated conditions
    is_systemic_related = models.BooleanField(
        _('Systemic Disease Related'),
        default=False,
        help_text=_('Diagnosis related to systemic disease')
    )
    
    systemic_factors = models.TextField(
        _('Systemic Factors'),
        blank=True,
        help_text=_('Related systemic conditions or factors')
    )
    
    # Risk factors
    risk_factors = models.TextField(
        _('Risk Factors'),
        blank=True,
        help_text=_('Identified risk factors contributing to condition')
    )
    
    # Status and monitoring
    DIAGNOSIS_STATUS_CHOICES = [
        ('ACTIVE', _('Active')),
        ('STABLE', _('Stable')),
        ('RESOLVED', _('Resolved')),
        ('CHRONIC', _('Chronic')),
        ('RECURRENT', _('Recurrent')),
    ]
    status = models.CharField(
        _('Diagnosis Status'),
        max_length=20,
        choices=DIAGNOSIS_STATUS_CHOICES,
        default='ACTIVE'
    )
    
    # Provider information
    diagnosing_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='made_diagnoses',
        verbose_name=_('Diagnosing Provider')
    )
    
    # Differential diagnosis
    differential_diagnoses = models.TextField(
        _('Differential Diagnoses'),
        blank=True,
        help_text=_('Other conditions considered in differential diagnosis')
    )

    
    # Follow-up and monitoring
    requires_monitoring = models.BooleanField(_('Requires Monitoring'), default=False)
    monitoring_interval_months = models.PositiveIntegerField(
        _('Monitoring Interval (Months)'),
        null=True,
        blank=True
    )
    next_evaluation_date = models.DateField(_('Next Evaluation Date'), null=True, blank=True)
    

    # Additional notes
    notes = models.TextField(_('Additional Notes'), blank=True)
    
    class Meta(IntEntity.Meta):
        verbose_name = _('Diagnosis')
        verbose_name_plural = _('Diagnoses')
        ordering = ['-diagnosis_date']
        default_permissions = {}
        indexes = [
            models.Index(fields=['patient', '-diagnosis_date']),
            models.Index(fields=['diagnosis_category']),
            models.Index(fields=['prognosis']),
            models.Index(fields=['status']),
            models.Index(fields=['case_sheet']),
        ]
    
    def __str__(self):
        return f"{self.patient.full_name}"
    
    @property
    def is_periodontal(self):
        """Check if diagnosis is periodontal-related"""
        return self.diagnosis_category == 'PERIODONTAL'
    
    @property
    def is_urgent(self):
        """Check if diagnosis requires urgent attention"""
        return self.prognosis == 'HOPELESS' or self.severity == 'SEVERE'
    
    @property
    def prognosis_color_code(self):
        """Return color code for prognosis display"""
        color_map = {
            'GOOD': 'green',
            'FAIR': 'amber',
            'POOR': 'orange',
            'HOPELESS': 'red',
        }
        return color_map.get(self.prognosis, 'gray')
    

    
    def get_similar_diagnoses(self):
        """Get similar diagnoses for AI reference (Phase II)"""
        # This would be implemented in Phase II with AI engine
        return Diagnosis.objects.filter(
            prognosis=self.prognosis
        ).exclude(id=self.id)[:5]
    