from django.db import models
from django.utils.translation import gettext_lazy as _
from django_lifecycle import hook, BEFORE_CREATE
from abstract.models import IntEntity
from django.conf import settings
import uuid


class Patient(IntEntity):
    """
    Patient model for managing patient demographics, contacts, and medical histories.
    Supports advanced search by name, ID, phone, diagnosis, treatment status, balance.
    """
    
    # User account relationship
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='patient_profile',
        help_text=_('Associated user account for patient authentication')
    )
    
    # Demographics
    clinic_id = models.CharField(
        _('Clinic ID'), 
        max_length=20, 
        unique=True, 
        help_text=_('Auto-generated unique clinic ID')
    )
    first_name = models.CharField(_('First Name'), max_length=100)
    last_name = models.CharField(_('Last Name'), max_length=100)
    middle_name = models.Char<PERSON>ield(_('Middle Name'), max_length=100, blank=True)
    date_of_birth = models.DateField(_('Date of Birth'), null=True, blank=True)
    
    GENDER_CHOICES = [
        ('M', _('Male')),
        ('F', _('Female')),
    ]
    gender = models.CharField(_('Gender'), max_length=1, choices=GENDER_CHOICES, blank=True)
    
    # Contact Information
    primary_phone = models.CharField(_('Primary Phone'), max_length=20, blank=True)
    secondary_phone = models.CharField(_('Secondary Phone'), max_length=20, blank=True)
    email = models.EmailField(_('Email'), blank=True)
    
    # Address
    address_line_1 = models.CharField(_('Address Line 1'), max_length=255, blank=True)
    address_line_2 = models.CharField(_('Address Line 2'), max_length=255, blank=True)
    city = models.CharField(_('City'), max_length=100, blank=True)
    state_province = models.CharField(_('State/Province'), max_length=100, blank=True)
    country = models.CharField(_('Country'), max_length=100, blank=True)
    
    # Emergency Contact
    emergency_contact_name = models.CharField(_('Emergency Contact Name'), max_length=200, blank=True)
    emergency_contact_phone = models.CharField(_('Emergency Contact Phone'), max_length=20, blank=True)
    emergency_contact_relationship = models.CharField(_('Emergency Contact Relationship'), max_length=100, blank=True)
    
    # Medical History
    medical_conditions = models.TextField(_('Medical Conditions'), blank=True, help_text=_('Current medical conditions'))
    medications = models.TextField(_('Current Medications'), blank=True, help_text=_('List of current medications'))
    allergies = models.TextField(_('Allergies'), blank=True, help_text=_('Known allergies'))
    
    # Dental History
    dental_history = models.TextField(_('Dental History'), blank=True, help_text=_('Previous dental treatments and conditions'))
    last_xray_date = models.DateField(_('Last X-ray Date'), null=True, blank=True)
    
    # Insurance Information
    insurance_provider = models.CharField(_('Insurance Provider'), max_length=200, blank=True)
    insurance_policy_number = models.CharField(_('Insurance Policy Number'), max_length=100, blank=True)
    insurance_group_number = models.CharField(_('Insurance Group Number'), max_length=100, blank=True)
    
    # Financial
    account_balance = models.DecimalField(_('Account Balance'), max_digits=10, decimal_places=2, default=0.00)
    loyalty_points = models.PositiveIntegerField(_('Loyalty Points'), default=0)
    
    # Status and Tracking
    PATIENT_STATUS_CHOICES = [
        ('ACTIVE', _('Active')),
        ('INACTIVE', _('Inactive')),
        ('ARCHIVED', _('Archived')),
        ('DECEASED', _('Deceased')),
    ]
    status = models.CharField(_('Patient Status'), max_length=20, choices=PATIENT_STATUS_CHOICES, default='ACTIVE')
    
    # Referral Information
    referred_by = models.CharField(_('Referred By'), max_length=200, blank=True)
    referral_source = models.CharField(_('Referral Source'), max_length=200, blank=True)
    
    # Preferences
    preferred_appointment_time = models.CharField(_('Preferred Appointment Time'), max_length=100, blank=True)
    communication_preference = models.CharField(
        _('Communication Preference'), 
        max_length=20, 
        choices=[
            ('EMAIL', _('Email')),
            ('SMS', _('SMS')),
            ('PHONE', _('Phone')),
            ('WHATSAPP', _('WhatsApp')),
        ], 
        default='EMAIL'
    )
    
    # Recall Information
    recall_due_date = models.DateField(_('Recall Due Date'), null=True, blank=True)
    recall_interval_months = models.PositiveIntegerField(_('Recall Interval (Months)'), default=6)
    
    class Meta(IntEntity.Meta):
        verbose_name = _('Patient')
        verbose_name_plural = _('Patients')
        ordering = ['last_name', 'first_name']
        default_permissions = {}
        indexes = [
            models.Index(fields=['clinic_id']),
            models.Index(fields=['last_name', 'first_name']),
            models.Index(fields=['primary_phone']),
            models.Index(fields=['email']),
            models.Index(fields=['status']),
            models.Index(fields=['recall_due_date']),
        ]
    
    def __str__(self):
        return f"{self.last_name}, {self.first_name} ({self.clinic_id})"
    
    @property
    def full_name(self):
        """Return the patient's full name"""
        if self.middle_name:
            return f"{self.first_name} {self.middle_name} {self.last_name}"
        return f"{self.first_name} {self.last_name}"
    
    @property
    def age(self):
        """Calculate patient's age"""
        if self.date_of_birth:
            from datetime import date
            today = date.today()
            return today.year - self.date_of_birth.year - (
                (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day)
            )
        return None
    
    @property
    def is_overdue_for_recall(self):
        """Check if patient is overdue for recall"""
        if self.recall_due_date:
            from datetime import date
            return date.today() > self.recall_due_date
        return False
    
    @property
    def has_medical_alerts(self):
        """Check if patient has medical alerts"""
        return bool(self.medical_alerts.strip())
    
    def create_user_account(self, password=None):
        """Create a user account for this patient if it doesn't exist"""
        if not self.user and self.primary_phone:
            from .user import PatientUser
            
            # Create the user account
            user_data = {
                'phone_number': self.primary_phone,
                'first_name': self.first_name,
                'last_name': self.last_name,
                'email': self.email,
            }
            
            if password:
                self.user = PatientUser.objects.create_user(password=password, **user_data)
            else:
                self.user = PatientUser.objects.create_user(**user_data)
            
            self.save(update_fields=['user'])
        
        return self.user
    
    def sync_user_data(self):
        """Synchronize patient data with associated user account"""
        if self.user:
            # Update user data from patient information
            self.user.first_name = self.first_name
            self.user.last_name = self.last_name
            self.user.email = self.email
            if self.primary_phone and self.user.phone_number != self.primary_phone:
                self.user.phone_number = self.primary_phone
            self.user.save()

    @hook(BEFORE_CREATE)
    def generate_clinic_id(self):
        """Generate clinic ID if not provided"""
        if not self.clinic_id:
            # Generate a unique clinic ID
            import random
            import string
            while True:
                clinic_id = 'P' + ''.join(random.choices(string.digits, k=6))
                if not Patient.objects.filter(clinic_id=clinic_id).exists():
                    self.clinic_id = clinic_id
                    break 