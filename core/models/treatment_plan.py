from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django_lifecycle import BEFORE_CREATE, BEFORE_SAVE, hook
from abstract.models import IntEntity
from .patient import Patient
from .diagnosis import Diagnosis
from .case_sheet import CaseSheet


class TreatmentPlan(IntEntity):
    """
    TreatmentPlan model for managing comprehensive treatment plans.
    Supports presentation to patients, e-signature approval, and one-click invoicing.
    """
    
    patient = models.ForeignKey(
        Patient,
        on_delete=models.CASCADE,
        related_name='treatment_plans',
        verbose_name=_('Patient')
    )
    
    case_sheet = models.ForeignKey(
        CaseSheet,
        on_delete=models.CASCADE,
        related_name='treatment_plans',
        verbose_name=_('Case Sheet'),
        null=True,
        blank=True
    )
    
    plan_number = models.CharField(
        _('Plan Number'),
        max_length=20,
        help_text=_('Auto-generated treatment plan number')
    )
    
    plan_date = models.DateTimeField(_('Plan Date'))
    
    # Plan status and workflow
    PLAN_STATUS_CHOICES = [
        ('DRAFT', _('Draft')),
        ('READY_FOR_PRESENTATION', _('Ready for Presentation')),
        ('PRESENTED', _('Presented to Patient')),
        ('ACCEPTED', _('Accepted by Patient')),
        ('PARTIALLY_ACCEPTED', _('Partially Accepted')),
        ('DECLINED', _('Declined by Patient')),
        ('IN_PROGRESS', _('In Progress')),
        ('COMPLETED', _('Completed')),
        ('CANCELLED', _('Cancelled')),
        ('EXPIRED', _('Expired')),
    ]
    status = models.CharField(
        _('Plan Status'),
        max_length=30,
        choices=PLAN_STATUS_CHOICES,
        default='DRAFT'
    )
    
    # Provider information
    primary_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='primary_treatment_plans',
        verbose_name=_('Primary Provider')
    )
    
    
    # Financial information
    total_estimated_cost = models.DecimalField(
        _('Total Estimated Cost'),
        max_digits=12,
        decimal_places=2,
        default=0.00
    )
    
    
    # Payment and financing
    PAYMENT_OPTION_CHOICES = [
        ('FULL_PAYMENT', _('Full Payment')),
        ('INSURANCE_COPAY', _('Insurance + Copay')),
        ('PAYMENT_PLAN', _('Payment Plan')),
        ('CASH_DISCOUNT', _('Cash Discount')),
        ('MEMBERSHIP_DISCOUNT', _('Membership Discount')),
    ]
    payment_option = models.CharField(
        _('Payment Option'),
        max_length=30,
        choices=PAYMENT_OPTION_CHOICES,
        default='INSURANCE_COPAY'
    )
    

    
    # Timing and scheduling
    estimated_total_duration_hours = models.DecimalField(
        _('Estimated Total Duration (Hours)'),
        max_digits=6,
        decimal_places=2,
        null=True,
        blank=True
    )
    
    
    
    # Follow-up and monitoring
    requires_followup = models.BooleanField(_('Requires Follow-up'), default=True)
    followup_interval_weeks = models.PositiveIntegerField(
        _('Follow-up Interval (Weeks)'),
        null=True,
        blank=True
    )
    
    
    # Notes and additional information
    notes = models.TextField(_('Notes'), blank=True)
    
    class Meta(IntEntity.Meta):
        verbose_name = _('Treatment Plan')
        verbose_name_plural = _('Treatment Plans')
        ordering = ['-plan_date']
        default_permissions = {}
        indexes = [
            models.Index(fields=['patient', '-plan_date']),
            models.Index(fields=['plan_number']),
            models.Index(fields=['status']),
            models.Index(fields=['case_sheet']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['plan_number', 'ten'],
                name='unique_plan_number_per_tenant'
            )
        ]
    
    def __str__(self):
        return f"Treatment Plan {self.plan_number} - {self.patient.full_name}"
    
    @property
    def is_accepted(self):
        """Check if plan is accepted by patient"""
        return self.status == 'ACCEPTED'
    
    @property
    def is_ready_for_invoice(self):
        """Check if plan is ready for one-click invoicing"""
        return self.status == 'ACCEPTED' and self.patient_consent_obtained
    
    @property
    def completion_percentage(self):
        """Calculate completion percentage based on completed items"""
        total_items = self.items.count()
        if total_items == 0:
            return 0
        completed_items = self.items.filter(status='COMPLETED').count()
        return round((completed_items / total_items) * 100, 1)
    
    @property
    def is_expired(self):
        """Check if treatment plan has expired"""
        if self.plan_expires_date:
            from datetime import date
            return date.today() > self.plan_expires_date
        return False
    
    @property
    def total_duration_display(self):
        """Return formatted total duration"""
        if self.estimated_total_duration_hours:
            hours = int(self.estimated_total_duration_hours)
            minutes = int((self.estimated_total_duration_hours - hours) * 60)
            if hours > 0:
                return f"{hours}h {minutes}m"
            return f"{minutes}m"
        return "TBD"
    
    def calculate_totals(self):
        """Calculate total costs from treatment plan items"""
        items = self.items.all()
        self.total_estimated_cost = sum(
            item.estimated_fee for item in items if item.estimated_fee
        )
        self.estimated_total_duration_hours = sum(
            item.estimated_duration_hours for item in items if item.estimated_duration_hours
        ) or 0
        
        # Calculate patient portion (simplified - would integrate with insurance)
        self.patient_portion = self.total_estimated_cost - self.insurance_portion
        
    @hook(BEFORE_CREATE)
    def generate_plan_number(self):
        """Generate plan number if not provided"""
        if not self.plan_number:
            from datetime import datetime
            import random
            import string
            
            date_str = datetime.now().strftime('%Y%m%d')
            while True:
                random_suffix = ''.join(random.choices(string.digits, k=3))
                plan_number = f"TP{date_str}{random_suffix}"
                if not TreatmentPlan.objects.filter(plan_number=plan_number).exists():
                    self.plan_number = plan_number
                    break


class TreatmentPlanItem(IntEntity):
    """
    Individual treatment items within a treatment plan.
    Each item represents a specific procedure or treatment.
    """
    
    treatment_plan = models.ForeignKey(
        TreatmentPlan,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name=_('Treatment Plan')
    )
    
    diagnosis = models.ForeignKey(
        Diagnosis,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='treatment_items',
        verbose_name=_('Related Diagnosis')
    )
    
    # Item details
    item_order = models.PositiveIntegerField(_('Item Order'), default=1)
    
    procedure_code = models.CharField(
        _('Procedure Code'),
        max_length=20,
        help_text=_('ADA or local procedure code')
    )
    
    procedure_description = models.CharField(
        _('Procedure Description'),
        max_length=500
    )
    
    # Clinical information
    tooth_numbers = models.CharField(
        _('Tooth Numbers'),
        max_length=100,
        blank=True,
        help_text=_('FDI tooth numbers involved (e.g., "16,17" or "11-14")')
    )
    
    surfaces = models.CharField(
        _('Surfaces'),
        max_length=20,
        blank=True,
        help_text=_('Tooth surfaces involved (e.g., "MOD", "DO")')
    )
    
    # Status and timing
    ITEM_STATUS_CHOICES = [
        ('PLANNED', _('Planned')),
        ('SCHEDULED', _('Scheduled')),
        ('IN_PROGRESS', _('In Progress')),
        ('COMPLETED', _('Completed')),
        ('CANCELLED', _('Cancelled')),
        ('DEFERRED', _('Deferred')),
    ]
    status = models.CharField(
        _('Item Status'),
        max_length=20,
        choices=ITEM_STATUS_CHOICES,
        default='PLANNED'
    )
    
    estimated_duration_hours = models.DecimalField(
        _('Estimated Duration (Hours)'),
        max_digits=4,
        decimal_places=2,
        null=True,
        blank=True
    )
    
    # Priority within the treatment plan
    ITEM_PRIORITY_CHOICES = [
        ('PHASE_1', _('Phase 1 - Emergency/Urgent')),
        ('PHASE_2', _('Phase 2 - Disease Control')),
        ('PHASE_3', _('Phase 3 - Definitive Treatment')),
        ('PHASE_4', _('Phase 4 - Maintenance')),
    ]
    treatment_phase = models.CharField(
        _('Treatment Phase'),
        max_length=20,
        choices=ITEM_PRIORITY_CHOICES,
        default='PHASE_3'
    )
    
    is_optional = models.BooleanField(
        _('Optional Treatment'),
        default=False,
        help_text=_('Treatment is optional/elective')
    )
    
    # Financial information
    estimated_fee = models.DecimalField(
        _('Estimated Fee'),
        max_digits=10,
        decimal_places=2,
        default=0.00
    )
    
    insurance_coverage_percentage = models.DecimalField(
        _('Insurance Coverage %'),
        max_digits=5,
        decimal_places=2,
        default=0.00
    )
    
    insurance_estimated_payment = models.DecimalField(
        _('Insurance Estimated Payment'),
        max_digits=10,
        decimal_places=2,
        default=0.00
    )
    
    patient_estimated_copay = models.DecimalField(
        _('Patient Estimated Copay'),
        max_digits=10,
        decimal_places=2,
        default=0.00
    )
    
    # Provider assignment
    assigned_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_treatment_items',
        verbose_name=_('Assigned Provider')
    )
    
    # Scheduling
    scheduled_date = models.DateTimeField(_('Scheduled Date'), null=True, blank=True)
    appointment_duration_minutes = models.PositiveIntegerField(
        _('Appointment Duration (Minutes)'),
        null=True,
        blank=True
    )
    
    # Dependencies
    prerequisite_items = models.ManyToManyField(
        'self',
        blank=True,
        symmetrical=False,
        related_name='dependent_items',
        verbose_name=_('Prerequisite Items'),
        help_text=_('Items that must be completed before this item')
    )
    
    # Patient preferences
    patient_accepted = models.BooleanField(_('Patient Accepted'), default=False)
    patient_declined = models.BooleanField(_('Patient Declined'), default=False)
    patient_deferred = models.BooleanField(_('Patient Deferred'), default=False)
    
    # Alternative options
    alternative_treatments = models.TextField(
        _('Alternative Treatments'),
        blank=True,
        help_text=_('Alternative treatment options discussed')
    )
    
    # Clinical notes
    clinical_notes = models.TextField(_('Clinical Notes'), blank=True)
    treatment_rationale = models.TextField(
        _('Treatment Rationale'),
        blank=True,
        help_text=_('Rationale for this specific treatment')
    )
    
    # Completion information
    completion_date = models.DateTimeField(_('Completion Date'), null=True, blank=True)
    treating_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='completed_treatment_items',
        verbose_name=_('Treating Provider')
    )
    
    actual_duration_minutes = models.PositiveIntegerField(
        _('Actual Duration (Minutes)'),
        null=True,
        blank=True
    )
    
    complications = models.TextField(_('Complications'), blank=True)
    outcome_notes = models.TextField(_('Outcome Notes'), blank=True)
    
    class Meta(IntEntity.Meta):
        verbose_name = _('Treatment Plan Item')
        verbose_name_plural = _('Treatment Plan Items')
        ordering = ['item_order']
        default_permissions = {}
        indexes = [
            models.Index(fields=['treatment_plan', 'item_order']),
            models.Index(fields=['status']),
            models.Index(fields=['treatment_phase']),
            models.Index(fields=['scheduled_date']),
        ]
    
    def __str__(self):
        return f"{self.treatment_plan.plan_number} - {self.procedure_description}"
    
    @property
    def is_ready_to_schedule(self):
        """Check if item is ready to be scheduled"""
        if self.status != 'PLANNED':
            return False
        
        # Check if all prerequisites are completed
        for prereq in self.prerequisite_items.all():
            if prereq.status != 'COMPLETED':
                return False
        
        return self.patient_accepted
    
    @property
    def estimated_duration_display(self):
        """Return formatted duration string"""
        if self.estimated_duration_hours:
            hours = int(self.estimated_duration_hours)
            minutes = int((self.estimated_duration_hours - hours) * 60)
            if hours > 0:
                return f"{hours}h {minutes}m"
            return f"{minutes}m"
        return "TBD"
    
    @property
    def phase_color(self):
        """Return color code for treatment phase"""
        color_map = {
            'PHASE_1': 'red',     # Emergency/Urgent
            'PHASE_2': 'orange',  # Disease Control
            'PHASE_3': 'blue',    # Definitive Treatment
            'PHASE_4': 'green',   # Maintenance
        }
        return color_map.get(self.treatment_phase, 'gray')
    
    def calculate_insurance_amounts(self):
        """Calculate insurance and patient portions"""
        if self.estimated_fee and self.insurance_coverage_percentage:
            self.insurance_estimated_payment = (
                self.estimated_fee * (self.insurance_coverage_percentage / 100)
            )
            self.patient_estimated_copay = (
                self.estimated_fee - self.insurance_estimated_payment
            )
    
    @hook(BEFORE_SAVE)
    def auto_calculate_insurance_amounts(self):
        """Auto-calculate insurance amounts"""
        self.calculate_insurance_amounts() 