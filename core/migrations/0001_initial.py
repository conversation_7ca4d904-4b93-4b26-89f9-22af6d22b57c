# Generated by Django 5.2.4 on 2025-07-10 14:50

import core.models.user
import django.core.validators
import django.db.models.deletion
import django.db.models.manager
import django.utils.timezone
import django_currentuser.db.models.fields
import django_currentuser.middleware
import django_lifecycle.mixins
import django_multitenant.fields
import django_multitenant.mixins
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('abstract', '__first__'),
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='ترميز قاعدة البيانات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('extra', models.JSONField(blank=True, default=dict, null=True)),
                ('username', models.CharField(blank=True, help_text='Required for Admin and Receptionist. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, null=True, unique=True, validators=[django.core.validators.RegexValidator(message='Username may only contain letters, numbers, and @/./+/-/_ characters.', regex='^[\\w.@+-]+$')], verbose_name='Username')),
                ('phone_number', models.CharField(blank=True, help_text='Required for Patient authentication', max_length=20, null=True, unique=True, validators=[django.core.validators.RegexValidator(message='Phone number must be entered in the format: "+999999999". Up to 15 digits allowed.', regex='^\\+?1?\\d{9,15}$')], verbose_name='Phone Number')),
                ('user_type', models.CharField(choices=[('admin', 'Admin'), ('receptionist', 'Receptionist'), ('patient', 'Patient')], default='patient', max_length=20, verbose_name='User Type')),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True, verbose_name='Last Login IP')),
                ('created_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_adder', to=settings.AUTH_USER_MODEL, verbose_name='المنظم')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('ten', django_multitenant.fields.TenantForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_tenant', to='abstract.tenant')),
                ('updated_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, on_update=True, related_name='%(app_label)s_%(class)s_updater', to=settings.AUTH_USER_MODEL, verbose_name='التعديل بواسطة')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'User',
                'verbose_name_plural': 'Users',
                'ordering': ['-created_at'],
                'abstract': False,
                'default_permissions': {},
                'base_manager_name': 'prefetch_manager',
            },
            bases=(django_multitenant.mixins.TenantModelMixin, django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ('objects', core.models.user.CustomUserManager()),
                ('prefetch_manager', django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name='Admin',
            fields=[
            ],
            options={
                'verbose_name': 'Admin',
                'verbose_name_plural': 'Admins',
                'abstract': False,
                'proxy': True,
                'default_permissions': {},
                'base_manager_name': 'prefetch_manager',
                'indexes': [],
                'constraints': [],
            },
            bases=('core.user',),
            managers=[
                ('objects', core.models.user.AdminManager()),
                ('prefetch_manager', django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name='PatientUser',
            fields=[
            ],
            options={
                'verbose_name': 'Patient User',
                'verbose_name_plural': 'Patient Users',
                'abstract': False,
                'proxy': True,
                'default_permissions': {},
                'base_manager_name': 'prefetch_manager',
                'indexes': [],
                'constraints': [],
            },
            bases=('core.user',),
            managers=[
                ('objects', core.models.user.PatientUserManager()),
                ('prefetch_manager', django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name='Receptionist',
            fields=[
            ],
            options={
                'verbose_name': 'Receptionist',
                'verbose_name_plural': 'Receptionists',
                'abstract': False,
                'proxy': True,
                'default_permissions': {},
                'base_manager_name': 'prefetch_manager',
                'indexes': [],
                'constraints': [],
            },
            bases=('core.user',),
            managers=[
                ('objects', core.models.user.ReceptionistManager()),
                ('prefetch_manager', django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name='CaseSheet',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='ترميز قاعدة البيانات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('extra', models.JSONField(blank=True, default=dict, null=True)),
                ('case_number', models.CharField(help_text='Auto-generated case number', max_length=20, verbose_name='Case Number')),
                ('visit_date', models.DateTimeField(verbose_name='Visit Date')),
                ('case_type', models.CharField(choices=[('CONSULTATION', 'Consultation'), ('EMERGENCY', 'Emergency'), ('ROUTINE_CHECKUP', 'Routine Checkup'), ('CLEANING', 'Cleaning'), ('TREATMENT', 'Treatment'), ('FOLLOW_UP', 'Follow-up'), ('SURGERY', 'Surgery'), ('ORTHODONTICS', 'Orthodontics')], default='CONSULTATION', max_length=20, verbose_name='Case Type')),
                ('chief_complaint', models.TextField(blank=True, verbose_name='Chief Complaint')),
                ('history_of_present_illness', models.TextField(blank=True, verbose_name='History of Present Illness')),
                ('clinical_notes', models.TextField(blank=True, verbose_name='Clinical Notes')),
                ('examination_findings', models.TextField(blank=True, verbose_name='Examination Findings')),
                ('blood_pressure_systolic', models.PositiveIntegerField(blank=True, null=True, verbose_name='Blood Pressure (Systolic)')),
                ('blood_pressure_diastolic', models.PositiveIntegerField(blank=True, null=True, verbose_name='Blood Pressure (Diastolic)')),
                ('pulse_rate', models.PositiveIntegerField(blank=True, null=True, verbose_name='Pulse Rate')),
                ('temperature', models.DecimalField(blank=True, decimal_places=1, max_digits=4, null=True, verbose_name='Temperature')),
                ('plaque_index', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True, verbose_name='Plaque Index')),
                ('gingival_index', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True, verbose_name='Gingival Index')),
                ('bleeding_on_probing', models.BooleanField(default=False, verbose_name='Bleeding on Probing')),
                ('probing_depth', models.PositiveIntegerField(blank=True, help_text='probing depth', null=True, validators=[django.core.validators.MaxValueValidator(15)], verbose_name='Probing Depth MB (mm)')),
                ('mobility', models.PositiveIntegerField(choices=[(0, 'No Mobility'), (1, 'Slight Mobility (1mm)'), (2, 'Moderate Mobility (1-2mm)'), (3, 'Severe Mobility (>2mm or vertical)')], default=0, verbose_name='Mobility')),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled'), ('REQUIRES_FOLLOWUP', 'Requires Follow-up')], default='DRAFT', max_length=20, verbose_name='Case Status')),
                ('estimated_duration_minutes', models.PositiveIntegerField(default=60, verbose_name='Estimated Duration (Minutes)')),
                ('actual_duration_minutes', models.PositiveIntegerField(blank=True, null=True, verbose_name='Actual Duration (Minutes)')),
                ('treatment_plan_presented', models.BooleanField(default=False, verbose_name='Treatment Plan Presented')),
                ('treatment_plan_accepted', models.BooleanField(default=False, verbose_name='Treatment Plan Accepted')),
                ('patient_signature_date', models.DateTimeField(blank=True, null=True, verbose_name='Patient Signature Date')),
                ('next_appointment_recommended', models.BooleanField(default=False, verbose_name='Next Appointment Recommended')),
                ('recommended_followup_weeks', models.PositiveIntegerField(blank=True, null=True, verbose_name='Recommended Follow-up (Weeks)')),
                ('insurance_claim_submitted', models.BooleanField(default=False, verbose_name='Insurance Claim Submitted')),
                ('insurance_claim_number', models.CharField(blank=True, max_length=100, verbose_name='Insurance Claim Number')),
                ('peer_review_required', models.BooleanField(default=False, verbose_name='Peer Review Required')),
                ('peer_review_completed', models.BooleanField(default=False, verbose_name='Peer Review Completed')),
                ('pain_level_before', models.PositiveIntegerField(blank=True, choices=[(0, 'No Pain'), (1, 'Mild Pain'), (2, 'Mild Pain'), (3, 'Moderate Pain'), (4, 'Moderate Pain'), (5, 'Moderate Pain'), (6, 'Severe Pain'), (7, 'Severe Pain'), (8, 'Very Severe Pain'), (9, 'Very Severe Pain'), (10, 'Worst Possible Pain')], null=True, verbose_name='Pain Level Before Treatment')),
                ('pain_level_after', models.PositiveIntegerField(blank=True, choices=[(0, 'No Pain'), (1, 'Mild Pain'), (2, 'Mild Pain'), (3, 'Moderate Pain'), (4, 'Moderate Pain'), (5, 'Moderate Pain'), (6, 'Severe Pain'), (7, 'Severe Pain'), (8, 'Very Severe Pain'), (9, 'Very Severe Pain'), (10, 'Worst Possible Pain')], null=True, verbose_name='Pain Level After Treatment')),
                ('patient_cooperation', models.TextField(blank=True, verbose_name='Patient Cooperation Notes')),
                ('complications', models.TextField(blank=True, verbose_name='Complications')),
                ('post_treatment_instructions', models.TextField(blank=True, verbose_name='Post-treatment Instructions')),
                ('assisting_providers', models.ManyToManyField(blank=True, related_name='assisting_case_sheets', to=settings.AUTH_USER_MODEL, verbose_name='Assisting Providers')),
                ('created_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_adder', to=settings.AUTH_USER_MODEL, verbose_name='المنظم')),
                ('peer_reviewer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='peer_reviewed_cases', to=settings.AUTH_USER_MODEL, verbose_name='Peer Reviewer')),
                ('primary_provider', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='primary_case_sheets', to=settings.AUTH_USER_MODEL, verbose_name='Primary Provider')),
                ('ten', django_multitenant.fields.TenantForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_tenant', to='abstract.tenant')),
                ('updated_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, on_update=True, related_name='%(app_label)s_%(class)s_updater', to=settings.AUTH_USER_MODEL, verbose_name='التعديل بواسطة')),
            ],
            options={
                'verbose_name': 'Case Sheet',
                'verbose_name_plural': 'Case Sheets',
                'ordering': ['-visit_date'],
                'abstract': False,
                'default_permissions': {},
                'base_manager_name': 'prefetch_manager',
            },
            bases=(django_multitenant.mixins.TenantModelMixin, django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ('objects', django.db.models.manager.Manager()),
                ('prefetch_manager', django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name='Patient',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='ترميز قاعدة البيانات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('extra', models.JSONField(blank=True, default=dict, null=True)),
                ('clinic_id', models.CharField(help_text='Auto-generated unique clinic ID', max_length=20, unique=True, verbose_name='Clinic ID')),
                ('first_name', models.CharField(max_length=100, verbose_name='First Name')),
                ('last_name', models.CharField(max_length=100, verbose_name='Last Name')),
                ('middle_name', models.CharField(blank=True, max_length=100, verbose_name='Middle Name')),
                ('date_of_birth', models.DateField(blank=True, null=True, verbose_name='Date of Birth')),
                ('gender', models.CharField(blank=True, choices=[('M', 'Male'), ('F', 'Female')], max_length=1, verbose_name='Gender')),
                ('primary_phone', models.CharField(blank=True, max_length=20, verbose_name='Primary Phone')),
                ('secondary_phone', models.CharField(blank=True, max_length=20, verbose_name='Secondary Phone')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='Email')),
                ('address_line_1', models.CharField(blank=True, max_length=255, verbose_name='Address Line 1')),
                ('address_line_2', models.CharField(blank=True, max_length=255, verbose_name='Address Line 2')),
                ('city', models.CharField(blank=True, max_length=100, verbose_name='City')),
                ('state_province', models.CharField(blank=True, max_length=100, verbose_name='State/Province')),
                ('country', models.CharField(blank=True, max_length=100, verbose_name='Country')),
                ('emergency_contact_name', models.CharField(blank=True, max_length=200, verbose_name='Emergency Contact Name')),
                ('emergency_contact_phone', models.CharField(blank=True, max_length=20, verbose_name='Emergency Contact Phone')),
                ('emergency_contact_relationship', models.CharField(blank=True, max_length=100, verbose_name='Emergency Contact Relationship')),
                ('medical_conditions', models.TextField(blank=True, help_text='Current medical conditions', verbose_name='Medical Conditions')),
                ('medications', models.TextField(blank=True, help_text='List of current medications', verbose_name='Current Medications')),
                ('allergies', models.TextField(blank=True, help_text='Known allergies', verbose_name='Allergies')),
                ('dental_history', models.TextField(blank=True, help_text='Previous dental treatments and conditions', verbose_name='Dental History')),
                ('last_xray_date', models.DateField(blank=True, null=True, verbose_name='Last X-ray Date')),
                ('insurance_provider', models.CharField(blank=True, max_length=200, verbose_name='Insurance Provider')),
                ('insurance_policy_number', models.CharField(blank=True, max_length=100, verbose_name='Insurance Policy Number')),
                ('insurance_group_number', models.CharField(blank=True, max_length=100, verbose_name='Insurance Group Number')),
                ('account_balance', models.DecimalField(decimal_places=2, default=0.0, max_digits=10, verbose_name='Account Balance')),
                ('loyalty_points', models.PositiveIntegerField(default=0, verbose_name='Loyalty Points')),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('INACTIVE', 'Inactive'), ('ARCHIVED', 'Archived'), ('DECEASED', 'Deceased')], default='ACTIVE', max_length=20, verbose_name='Patient Status')),
                ('referred_by', models.CharField(blank=True, max_length=200, verbose_name='Referred By')),
                ('referral_source', models.CharField(blank=True, max_length=200, verbose_name='Referral Source')),
                ('preferred_appointment_time', models.CharField(blank=True, max_length=100, verbose_name='Preferred Appointment Time')),
                ('communication_preference', models.CharField(choices=[('EMAIL', 'Email'), ('SMS', 'SMS'), ('PHONE', 'Phone'), ('WHATSAPP', 'WhatsApp')], default='EMAIL', max_length=20, verbose_name='Communication Preference')),
                ('recall_due_date', models.DateField(blank=True, null=True, verbose_name='Recall Due Date')),
                ('recall_interval_months', models.PositiveIntegerField(default=6, verbose_name='Recall Interval (Months)')),
                ('created_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_adder', to=settings.AUTH_USER_MODEL, verbose_name='المنظم')),
                ('ten', django_multitenant.fields.TenantForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_tenant', to='abstract.tenant')),
                ('updated_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, on_update=True, related_name='%(app_label)s_%(class)s_updater', to=settings.AUTH_USER_MODEL, verbose_name='التعديل بواسطة')),
                ('user', models.OneToOneField(blank=True, help_text='Associated user account for patient authentication', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='patient_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Patient',
                'verbose_name_plural': 'Patients',
                'ordering': ['last_name', 'first_name'],
                'abstract': False,
                'default_permissions': {},
                'base_manager_name': 'prefetch_manager',
            },
            bases=(django_multitenant.mixins.TenantModelMixin, django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ('objects', django.db.models.manager.Manager()),
                ('prefetch_manager', django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name='Invoice',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='ترميز قاعدة البيانات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('extra', models.JSONField(blank=True, default=dict, null=True)),
                ('invoice_number', models.CharField(help_text='Auto-generated invoice number', max_length=20, verbose_name='Invoice Number')),
                ('invoice_date', models.DateTimeField(verbose_name='Invoice Date')),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('PENDING', 'Pending'), ('SENT', 'Sent to Patient'), ('VIEWED', 'Viewed by Patient'), ('PARTIAL_PAYMENT', 'Partial Payment'), ('PAID', 'Paid in Full'), ('OVERDUE', 'Overdue'), ('CANCELLED', 'Cancelled'), ('REFUNDED', 'Refunded'), ('WRITE_OFF', 'Write Off')], default='DRAFT', max_length=20, verbose_name='Invoice Status')),
                ('subtotal', models.DecimalField(decimal_places=2, default=0.0, max_digits=12, verbose_name='Subtotal')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=12, verbose_name='Total Amount')),
                ('amount_paid', models.DecimalField(decimal_places=2, default=0.0, max_digits=12, verbose_name='Amount Paid')),
                ('balance_due', models.DecimalField(decimal_places=2, default=0.0, max_digits=12, verbose_name='Balance Due')),
                ('payment_terms_days', models.PositiveIntegerField(default=30, help_text='Number of days until payment is due', verbose_name='Payment Terms (Days)')),
                ('due_date', models.DateField(verbose_name='Due Date')),
                ('text_to_pay_sent', models.BooleanField(default=False, verbose_name='Text-to-Pay Sent')),
                ('text_to_pay_link', models.URLField(blank=True, verbose_name='Text-to-Pay Link')),
                ('created_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_adder', to=settings.AUTH_USER_MODEL, verbose_name='المنظم')),
                ('ten', django_multitenant.fields.TenantForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_tenant', to='abstract.tenant')),
                ('updated_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, on_update=True, related_name='%(app_label)s_%(class)s_updater', to=settings.AUTH_USER_MODEL, verbose_name='التعديل بواسطة')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invoices', to='core.patient', verbose_name='Patient')),
            ],
            options={
                'verbose_name': 'Invoice',
                'verbose_name_plural': 'Invoices',
                'ordering': ['-invoice_date'],
                'abstract': False,
                'default_permissions': {},
                'base_manager_name': 'prefetch_manager',
            },
            bases=(django_multitenant.mixins.TenantModelMixin, django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ('objects', django.db.models.manager.Manager()),
                ('prefetch_manager', django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name='Diagnosis',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='ترميز قاعدة البيانات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('extra', models.JSONField(blank=True, default=dict, null=True)),
                ('diagnosis_date', models.DateTimeField(verbose_name='Diagnosis Date')),
                ('diagnosis_category', models.CharField(choices=[('CARIES', 'Dental Caries'), ('PERIODONTAL', 'Periodontal Disease'), ('ENDODONTIC', 'Endodontic'), ('ORAL_PATHOLOGY', 'Oral Pathology'), ('ORTHODONTIC', 'Orthodontic'), ('ORAL_SURGERY', 'Oral Surgery'), ('PROSTHODONTIC', 'Prosthodontic'), ('PREVENTIVE', 'Preventive'), ('TMJ', 'TMJ Disorders'), ('TRAUMA', 'Trauma'), ('CONGENITAL', 'Congenital Anomalies'), ('OTHER', 'Other')], max_length=20, verbose_name='Diagnosis Category')),
                ('prognosis', models.CharField(choices=[('GOOD', 'Good'), ('FAIR', 'Fair'), ('POOR', 'Poor'), ('HOPELESS', 'Hopeless')], help_text='Prognosis scale aligned with periodontal literature', max_length=20, verbose_name='Prognosis')),
                ('clinical_description', models.TextField(help_text='Detailed clinical description of the diagnosis', verbose_name='Clinical Description')),
                ('etiology', models.TextField(blank=True, help_text='Suspected cause or contributing factors', verbose_name='Etiology')),
                ('severity', models.CharField(blank=True, choices=[('MILD', 'Mild'), ('MODERATE', 'Moderate'), ('SEVERE', 'Severe'), ('EXTENSIVE', 'Extensive')], max_length=20, verbose_name='Severity')),
                ('periodontal_stage', models.CharField(blank=True, choices=[('STAGE_I', 'Stage I - Initial'), ('STAGE_II', 'Stage II - Moderate'), ('STAGE_III', 'Stage III - Severe with potential for additional tooth loss'), ('STAGE_IV', 'Stage IV - Severe with extensive tooth loss')], help_text='AAP 2017 Classification', max_length=20, verbose_name='Periodontal Stage')),
                ('periodontal_grade', models.CharField(blank=True, choices=[('GRADE_A', 'Grade A - Slow rate of progression'), ('GRADE_B', 'Grade B - Moderate rate of progression'), ('GRADE_C', 'Grade C - Rapid rate of progression')], help_text='AAP 2017 Classification', max_length=20, verbose_name='Periodontal Grade')),
                ('is_systemic_related', models.BooleanField(default=False, help_text='Diagnosis related to systemic disease', verbose_name='Systemic Disease Related')),
                ('systemic_factors', models.TextField(blank=True, help_text='Related systemic conditions or factors', verbose_name='Systemic Factors')),
                ('risk_factors', models.TextField(blank=True, help_text='Identified risk factors contributing to condition', verbose_name='Risk Factors')),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('STABLE', 'Stable'), ('RESOLVED', 'Resolved'), ('CHRONIC', 'Chronic'), ('RECURRENT', 'Recurrent')], default='ACTIVE', max_length=20, verbose_name='Diagnosis Status')),
                ('differential_diagnoses', models.TextField(blank=True, help_text='Other conditions considered in differential diagnosis', verbose_name='Differential Diagnoses')),
                ('requires_monitoring', models.BooleanField(default=False, verbose_name='Requires Monitoring')),
                ('monitoring_interval_months', models.PositiveIntegerField(blank=True, null=True, verbose_name='Monitoring Interval (Months)')),
                ('next_evaluation_date', models.DateField(blank=True, null=True, verbose_name='Next Evaluation Date')),
                ('notes', models.TextField(blank=True, verbose_name='Additional Notes')),
                ('case_sheet', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='diagnoses', to='core.casesheet', verbose_name='Case Sheet')),
                ('created_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_adder', to=settings.AUTH_USER_MODEL, verbose_name='المنظم')),
                ('diagnosing_provider', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='made_diagnoses', to=settings.AUTH_USER_MODEL, verbose_name='Diagnosing Provider')),
                ('ten', django_multitenant.fields.TenantForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_tenant', to='abstract.tenant')),
                ('updated_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, on_update=True, related_name='%(app_label)s_%(class)s_updater', to=settings.AUTH_USER_MODEL, verbose_name='التعديل بواسطة')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='diagnoses', to='core.patient', verbose_name='Patient')),
            ],
            options={
                'verbose_name': 'Diagnosis',
                'verbose_name_plural': 'Diagnoses',
                'ordering': ['-diagnosis_date'],
                'abstract': False,
                'default_permissions': {},
                'base_manager_name': 'prefetch_manager',
            },
            bases=(django_multitenant.mixins.TenantModelMixin, django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ('objects', django.db.models.manager.Manager()),
                ('prefetch_manager', django.db.models.manager.Manager()),
            ],
        ),
        migrations.AddField(
            model_name='casesheet',
            name='patient',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='case_sheets', to='core.patient', verbose_name='Patient'),
        ),
        migrations.CreateModel(
            name='Adjustment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='ترميز قاعدة البيانات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('extra', models.JSONField(blank=True, default=dict, null=True)),
                ('adjustment_number', models.CharField(help_text='Auto-generated adjustment number', max_length=20, verbose_name='Adjustment Number')),
                ('adjustment_date', models.DateTimeField(verbose_name='Adjustment Date')),
                ('adjustment_type', models.CharField(choices=[('DISCOUNT', 'Discount'), ('WRITE_OFF', 'Write Off'), ('COURTESY_ADJUSTMENT', 'Courtesy Adjustment'), ('INSURANCE_ADJUSTMENT', 'Insurance Adjustment'), ('PROMPT_PAY_DISCOUNT', 'Prompt Pay Discount'), ('FAMILY_DISCOUNT', 'Family Discount'), ('SENIOR_DISCOUNT', 'Senior Discount'), ('HARDSHIP_ADJUSTMENT', 'Hardship Adjustment'), ('BILLING_ERROR', 'Billing Error Correction'), ('OTHER', 'Other')], max_length=30, verbose_name='Adjustment Type')),
                ('amount', models.DecimalField(decimal_places=2, help_text='Positive for credits, negative for charges', max_digits=10, verbose_name='Adjustment Amount')),
                ('reason', models.TextField(help_text='Reason for adjustment', verbose_name='Reason')),
                ('status', models.CharField(choices=[('PENDING', 'Pending Approval'), ('APPROVED', 'Approved'), ('DENIED', 'Denied'), ('APPLIED', 'Applied')], default='PENDING', max_length=20, verbose_name='Status')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_adjustments', to=settings.AUTH_USER_MODEL, verbose_name='Approved By')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_adjustments', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
                ('ten', django_multitenant.fields.TenantForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_tenant', to='abstract.tenant')),
                ('updated_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, on_update=True, related_name='%(app_label)s_%(class)s_updater', to=settings.AUTH_USER_MODEL, verbose_name='التعديل بواسطة')),
                ('invoice', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='adjustments', to='core.invoice', verbose_name='Invoice')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='adjustments', to='core.patient', verbose_name='Patient')),
            ],
            options={
                'verbose_name': 'Adjustment',
                'verbose_name_plural': 'Adjustments',
                'ordering': ['-adjustment_date'],
                'abstract': False,
                'default_permissions': {},
                'base_manager_name': 'prefetch_manager',
            },
            bases=(django_multitenant.mixins.TenantModelMixin, django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ('objects', django.db.models.manager.Manager()),
                ('prefetch_manager', django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='ترميز قاعدة البيانات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('extra', models.JSONField(blank=True, default=dict, null=True)),
                ('payment_number', models.CharField(help_text='Auto-generated payment number', max_length=20, verbose_name='Payment Number')),
                ('payment_date', models.DateTimeField(verbose_name='Payment Date')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='Payment Amount')),
                ('applied_amount', models.DecimalField(decimal_places=2, default=0.0, help_text='Amount applied to invoices', max_digits=10, verbose_name='Applied Amount')),
                ('unapplied_amount', models.DecimalField(decimal_places=2, default=0.0, help_text='Amount not yet applied to invoices', max_digits=10, verbose_name='Unapplied Amount')),
                ('payment_method', models.CharField(choices=[('CASH', 'Cash'), ('CHECK', 'Check'), ('CREDIT_CARD', 'Credit Card'), ('DEBIT_CARD', 'Debit Card'), ('BANK_TRANSFER', 'Bank Transfer'), ('ACH', 'ACH/Electronic Check'), ('PAYPAL', 'PayPal'), ('VENMO', 'Venmo'), ('APPLE_PAY', 'Apple Pay'), ('GOOGLE_PAY', 'Google Pay'), ('INSURANCE', 'Insurance Payment'), ('CARE_CREDIT', 'CareCredit'), ('PAYMENT_PLAN', 'Payment Plan'), ('GIFT_CARD', 'Gift Card'), ('LOYALTY_POINTS', 'Loyalty Points'), ('OTHER', 'Other')], max_length=20, verbose_name='Payment Method')),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('PROCESSING', 'Processing'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('CANCELLED', 'Cancelled'), ('REFUNDED', 'Refunded'), ('DISPUTED', 'Disputed'), ('CHARGEBACK', 'Chargeback')], default='COMPLETED', max_length=20, verbose_name='Payment Status')),
                ('transaction_id', models.CharField(blank=True, help_text='External transaction ID from payment processor', max_length=100, verbose_name='Transaction ID')),
                ('processor_response', models.TextField(blank=True, help_text='Response from payment processor', verbose_name='Processor Response')),
                ('check_number', models.CharField(blank=True, max_length=50, verbose_name='Check Number')),
                ('bank_name', models.CharField(blank=True, max_length=200, verbose_name='Bank Name')),
                ('card_last_four', models.CharField(blank=True, max_length=4, verbose_name='Card Last 4 Digits')),
                ('card_type', models.CharField(blank=True, choices=[('VISA', 'Visa'), ('MASTERCARD', 'Mastercard'), ('AMEX', 'American Express'), ('DISCOVER', 'Discover'), ('OTHER', 'Other')], max_length=20, verbose_name='Card Type')),
                ('processing_fee', models.DecimalField(decimal_places=2, default=0.0, max_digits=8, verbose_name='Processing Fee')),
                ('net_amount', models.DecimalField(decimal_places=2, default=0.0, help_text='Payment amount minus processing fees', max_digits=10, verbose_name='Net Amount')),
                ('payment_source', models.CharField(choices=[('FRONT_DESK', 'Front Desk'), ('ONLINE', 'Online Payment'), ('PHONE', 'Phone Payment'), ('MAIL', 'Mail'), ('MOBILE_APP', 'Mobile App'), ('TEXT_TO_PAY', 'Text-to-Pay'), ('AUTO_PAY', 'Auto-pay'), ('KIOSK', 'Self-service Kiosk')], default='FRONT_DESK', max_length=20, verbose_name='Payment Source')),
                ('is_deposited', models.BooleanField(default=False, verbose_name='Deposited')),
                ('deposit_date', models.DateField(blank=True, null=True, verbose_name='Deposit Date')),
                ('deposit_batch', models.CharField(blank=True, max_length=50, verbose_name='Deposit Batch')),
                ('is_refunded', models.BooleanField(default=False, verbose_name='Refunded')),
                ('refund_date', models.DateTimeField(blank=True, null=True, verbose_name='Refund Date')),
                ('refund_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Refund Amount')),
                ('refund_reason', models.TextField(blank=True, verbose_name='Refund Reason')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('internal_notes', models.TextField(blank=True, verbose_name='Internal Notes')),
                ('receipt_sent', models.BooleanField(default=False, verbose_name='Receipt Sent')),
                ('receipt_email', models.EmailField(blank=True, max_length=254, verbose_name='Receipt Email')),
                ('receipt_method', models.CharField(choices=[('EMAIL', 'Email'), ('SMS', 'SMS'), ('PRINT', 'Print'), ('NONE', 'No Receipt')], default='EMAIL', max_length=20, verbose_name='Receipt Method')),
                ('created_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_adder', to=settings.AUTH_USER_MODEL, verbose_name='المنظم')),
                ('invoice', models.ForeignKey(blank=True, help_text='Invoice this payment is applied to', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payments', to='core.invoice', verbose_name='Invoice')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='core.patient', verbose_name='Patient')),
                ('processed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_payments', to=settings.AUTH_USER_MODEL, verbose_name='Processed By')),
                ('received_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='received_payments', to=settings.AUTH_USER_MODEL, verbose_name='Received By')),
                ('ten', django_multitenant.fields.TenantForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_tenant', to='abstract.tenant')),
                ('updated_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, on_update=True, related_name='%(app_label)s_%(class)s_updater', to=settings.AUTH_USER_MODEL, verbose_name='التعديل بواسطة')),
            ],
            options={
                'verbose_name': 'Payment',
                'verbose_name_plural': 'Payments',
                'ordering': ['-payment_date'],
                'abstract': False,
                'default_permissions': {},
                'base_manager_name': 'prefetch_manager',
            },
            bases=(django_multitenant.mixins.TenantModelMixin, django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ('objects', django.db.models.manager.Manager()),
                ('prefetch_manager', django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name='PaymentApplication',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='ترميز قاعدة البيانات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('extra', models.JSONField(blank=True, default=dict, null=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0.01)], verbose_name='Applied Amount')),
                ('application_date', models.DateTimeField(verbose_name='Application Date')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('applied_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='applied_payments', to=settings.AUTH_USER_MODEL, verbose_name='Applied By')),
                ('created_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_adder', to=settings.AUTH_USER_MODEL, verbose_name='المنظم')),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_applications', to='core.invoice', verbose_name='Invoice')),
                ('payment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='applications', to='core.payment', verbose_name='Payment')),
                ('ten', django_multitenant.fields.TenantForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_tenant', to='abstract.tenant')),
                ('updated_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, on_update=True, related_name='%(app_label)s_%(class)s_updater', to=settings.AUTH_USER_MODEL, verbose_name='التعديل بواسطة')),
            ],
            options={
                'verbose_name': 'Payment Application',
                'verbose_name_plural': 'Payment Applications',
                'ordering': ['-application_date'],
                'abstract': False,
                'default_permissions': {},
                'base_manager_name': 'prefetch_manager',
            },
            bases=(django_multitenant.mixins.TenantModelMixin, django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ('objects', django.db.models.manager.Manager()),
                ('prefetch_manager', django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name='Tooth',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='ترميز قاعدة البيانات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('extra', models.JSONField(blank=True, default=dict, null=True)),
                ('fdi_number', models.PositiveIntegerField(help_text='FDI two-digit tooth notation (11-48 for adults, 51-85 for children)', validators=[django.core.validators.MinValueValidator(11), django.core.validators.MaxValueValidator(85)], verbose_name='FDI Number')),
                ('tooth_type', models.CharField(choices=[('PERMANENT', 'Permanent'), ('DECIDUOUS', 'Deciduous/Primary')], help_text='Permanent (adult) or deciduous (child) tooth', max_length=20, verbose_name='Tooth Type')),
                ('tooth_class', models.CharField(choices=[('INCISOR', 'Incisor'), ('CANINE', 'Canine'), ('PREMOLAR', 'Premolar'), ('MOLAR', 'Molar')], max_length=20, verbose_name='Tooth Class')),
                ('quadrant', models.PositiveIntegerField(choices=[(1, 'Upper Right (1)'), (2, 'Upper Left (2)'), (3, 'Lower Left (3)'), (4, 'Lower Right (4)')], verbose_name='Quadrant')),
                ('position_in_quadrant', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(8)], verbose_name='Position in Quadrant')),
                ('status', models.CharField(choices=[('PRESENT', 'Present'), ('MISSING', 'Missing'), ('EXTRACTED', 'Extracted'), ('WEAR', 'Wear'), ('DISCOLORATION', 'Discoloration'), ('ATTRITION', 'Attrition'), ('IMPACTED', 'Impacted'), ('CONGENITALLY_MISSING', 'Congenitally Missing'), ('SUPERNUMERARY', 'Supernumerary'), ('ROOT_FRAGMENT', 'Root Fragment'), ('IMPLANT', 'Implant'), ('BRIDGE_ABUTMENT', 'Bridge Abutment'), ('CROWN', 'Crown')], default='PRESENT', max_length=30, verbose_name='Tooth Status')),
                ('is_vital', models.BooleanField(default=True, verbose_name='Vital')),
                ('pulp_test_result', models.CharField(blank=True, help_text='Results of vitality testing', max_length=100, verbose_name='Pulp Test Result')),
                ('percussion_test', models.CharField(choices=[('NEGATIVE', 'Negative'), ('POSITIVE', 'Positive'), ('NOT_TESTED', 'Not Tested')], default='NOT_TESTED', max_length=20, verbose_name='Percussion Test')),
                ('palpation_test', models.CharField(choices=[('NEGATIVE', 'Negative'), ('POSITIVE', 'Positive'), ('NOT_TESTED', 'Not Tested')], default='NOT_TESTED', max_length=20, verbose_name='Palpation Test')),
                ('treatment_priority', models.CharField(choices=[('EMERGENCY', 'Emergency'), ('URGENT', 'Urgent'), ('ROUTINE', 'Routine'), ('ELECTIVE', 'Elective'), ('NONE', 'No Treatment Needed')], default='NONE', max_length=20, verbose_name='Treatment Priority')),
                ('clinical_notes', models.TextField(blank=True, verbose_name='Clinical Notes')),
                ('patient_symptoms', models.TextField(blank=True, verbose_name='Patient Symptoms')),
                ('last_examined_date', models.DateField(blank=True, null=True, verbose_name='Last Examined')),
                ('last_treatment_date', models.DateField(blank=True, null=True, verbose_name='Last Treatment')),
                ('created_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_adder', to=settings.AUTH_USER_MODEL, verbose_name='المنظم')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='teeth', to='core.patient', verbose_name='Patient')),
                ('ten', django_multitenant.fields.TenantForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_tenant', to='abstract.tenant')),
                ('updated_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, on_update=True, related_name='%(app_label)s_%(class)s_updater', to=settings.AUTH_USER_MODEL, verbose_name='التعديل بواسطة')),
            ],
            options={
                'verbose_name': 'Tooth',
                'verbose_name_plural': 'Teeth',
                'ordering': ['fdi_number'],
                'abstract': False,
                'default_permissions': {},
                'base_manager_name': 'prefetch_manager',
            },
            bases=(django_multitenant.mixins.TenantModelMixin, django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ('objects', django.db.models.manager.Manager()),
                ('prefetch_manager', django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name='TreatmentPlan',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='ترميز قاعدة البيانات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('extra', models.JSONField(blank=True, default=dict, null=True)),
                ('plan_number', models.CharField(help_text='Auto-generated treatment plan number', max_length=20, verbose_name='Plan Number')),
                ('plan_date', models.DateTimeField(verbose_name='Plan Date')),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('READY_FOR_PRESENTATION', 'Ready for Presentation'), ('PRESENTED', 'Presented to Patient'), ('ACCEPTED', 'Accepted by Patient'), ('PARTIALLY_ACCEPTED', 'Partially Accepted'), ('DECLINED', 'Declined by Patient'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled'), ('EXPIRED', 'Expired')], default='DRAFT', max_length=30, verbose_name='Plan Status')),
                ('total_estimated_cost', models.DecimalField(decimal_places=2, default=0.0, max_digits=12, verbose_name='Total Estimated Cost')),
                ('payment_option', models.CharField(choices=[('FULL_PAYMENT', 'Full Payment'), ('INSURANCE_COPAY', 'Insurance + Copay'), ('PAYMENT_PLAN', 'Payment Plan'), ('CASH_DISCOUNT', 'Cash Discount'), ('MEMBERSHIP_DISCOUNT', 'Membership Discount')], default='INSURANCE_COPAY', max_length=30, verbose_name='Payment Option')),
                ('estimated_total_duration_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True, verbose_name='Estimated Total Duration (Hours)')),
                ('requires_followup', models.BooleanField(default=True, verbose_name='Requires Follow-up')),
                ('followup_interval_weeks', models.PositiveIntegerField(blank=True, null=True, verbose_name='Follow-up Interval (Weeks)')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('case_sheet', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='treatment_plans', to='core.casesheet', verbose_name='Case Sheet')),
                ('created_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_adder', to=settings.AUTH_USER_MODEL, verbose_name='المنظم')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='treatment_plans', to='core.patient', verbose_name='Patient')),
                ('primary_provider', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='primary_treatment_plans', to=settings.AUTH_USER_MODEL, verbose_name='Primary Provider')),
                ('ten', django_multitenant.fields.TenantForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_tenant', to='abstract.tenant')),
                ('updated_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, on_update=True, related_name='%(app_label)s_%(class)s_updater', to=settings.AUTH_USER_MODEL, verbose_name='التعديل بواسطة')),
            ],
            options={
                'verbose_name': 'Treatment Plan',
                'verbose_name_plural': 'Treatment Plans',
                'ordering': ['-plan_date'],
                'abstract': False,
                'default_permissions': {},
                'base_manager_name': 'prefetch_manager',
            },
            bases=(django_multitenant.mixins.TenantModelMixin, django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ('objects', django.db.models.manager.Manager()),
                ('prefetch_manager', django.db.models.manager.Manager()),
            ],
        ),
        migrations.AddField(
            model_name='invoice',
            name='treatment_plan',
            field=models.ForeignKey(blank=True, help_text='Treatment plan this invoice was generated from', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='invoices', to='core.treatmentplan', verbose_name='Treatment Plan'),
        ),
        migrations.CreateModel(
            name='TreatmentPlanItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='ترميز قاعدة البيانات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('extra', models.JSONField(blank=True, default=dict, null=True)),
                ('item_order', models.PositiveIntegerField(default=1, verbose_name='Item Order')),
                ('procedure_code', models.CharField(help_text='ADA or local procedure code', max_length=20, verbose_name='Procedure Code')),
                ('procedure_description', models.CharField(max_length=500, verbose_name='Procedure Description')),
                ('tooth_numbers', models.CharField(blank=True, help_text='FDI tooth numbers involved (e.g., "16,17" or "11-14")', max_length=100, verbose_name='Tooth Numbers')),
                ('surfaces', models.CharField(blank=True, help_text='Tooth surfaces involved (e.g., "MOD", "DO")', max_length=20, verbose_name='Surfaces')),
                ('status', models.CharField(choices=[('PLANNED', 'Planned'), ('SCHEDULED', 'Scheduled'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled'), ('DEFERRED', 'Deferred')], default='PLANNED', max_length=20, verbose_name='Item Status')),
                ('estimated_duration_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True, verbose_name='Estimated Duration (Hours)')),
                ('treatment_phase', models.CharField(choices=[('PHASE_1', 'Phase 1 - Emergency/Urgent'), ('PHASE_2', 'Phase 2 - Disease Control'), ('PHASE_3', 'Phase 3 - Definitive Treatment'), ('PHASE_4', 'Phase 4 - Maintenance')], default='PHASE_3', max_length=20, verbose_name='Treatment Phase')),
                ('is_optional', models.BooleanField(default=False, help_text='Treatment is optional/elective', verbose_name='Optional Treatment')),
                ('estimated_fee', models.DecimalField(decimal_places=2, default=0.0, max_digits=10, verbose_name='Estimated Fee')),
                ('insurance_coverage_percentage', models.DecimalField(decimal_places=2, default=0.0, max_digits=5, verbose_name='Insurance Coverage %')),
                ('insurance_estimated_payment', models.DecimalField(decimal_places=2, default=0.0, max_digits=10, verbose_name='Insurance Estimated Payment')),
                ('patient_estimated_copay', models.DecimalField(decimal_places=2, default=0.0, max_digits=10, verbose_name='Patient Estimated Copay')),
                ('scheduled_date', models.DateTimeField(blank=True, null=True, verbose_name='Scheduled Date')),
                ('appointment_duration_minutes', models.PositiveIntegerField(blank=True, null=True, verbose_name='Appointment Duration (Minutes)')),
                ('patient_accepted', models.BooleanField(default=False, verbose_name='Patient Accepted')),
                ('patient_declined', models.BooleanField(default=False, verbose_name='Patient Declined')),
                ('patient_deferred', models.BooleanField(default=False, verbose_name='Patient Deferred')),
                ('alternative_treatments', models.TextField(blank=True, help_text='Alternative treatment options discussed', verbose_name='Alternative Treatments')),
                ('clinical_notes', models.TextField(blank=True, verbose_name='Clinical Notes')),
                ('treatment_rationale', models.TextField(blank=True, help_text='Rationale for this specific treatment', verbose_name='Treatment Rationale')),
                ('completion_date', models.DateTimeField(blank=True, null=True, verbose_name='Completion Date')),
                ('actual_duration_minutes', models.PositiveIntegerField(blank=True, null=True, verbose_name='Actual Duration (Minutes)')),
                ('complications', models.TextField(blank=True, verbose_name='Complications')),
                ('outcome_notes', models.TextField(blank=True, verbose_name='Outcome Notes')),
                ('assigned_provider', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_treatment_items', to=settings.AUTH_USER_MODEL, verbose_name='Assigned Provider')),
                ('created_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(app_label)s_%(class)s_adder', to=settings.AUTH_USER_MODEL, verbose_name='المنظم')),
                ('diagnosis', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='treatment_items', to='core.diagnosis', verbose_name='Related Diagnosis')),
                ('prerequisite_items', models.ManyToManyField(blank=True, help_text='Items that must be completed before this item', related_name='dependent_items', to='core.treatmentplanitem', verbose_name='Prerequisite Items')),
                ('ten', django_multitenant.fields.TenantForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_tenant', to='abstract.tenant')),
                ('treating_provider', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='completed_treatment_items', to=settings.AUTH_USER_MODEL, verbose_name='Treating Provider')),
                ('treatment_plan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='core.treatmentplan', verbose_name='Treatment Plan')),
                ('updated_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, on_update=True, related_name='%(app_label)s_%(class)s_updater', to=settings.AUTH_USER_MODEL, verbose_name='التعديل بواسطة')),
            ],
            options={
                'verbose_name': 'Treatment Plan Item',
                'verbose_name_plural': 'Treatment Plan Items',
                'ordering': ['item_order'],
                'abstract': False,
                'default_permissions': {},
                'base_manager_name': 'prefetch_manager',
            },
            bases=(django_multitenant.mixins.TenantModelMixin, django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ('objects', django.db.models.manager.Manager()),
                ('prefetch_manager', django.db.models.manager.Manager()),
            ],
        ),
        migrations.CreateModel(
            name='Appointment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='ترميز قاعدة البيانات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('extra', models.JSONField(blank=True, default=dict, null=True)),
                ('appointment_number', models.CharField(help_text='Auto-generated appointment number', max_length=20, verbose_name='Appointment Number')),
                ('appointment_date', models.DateTimeField(verbose_name='Appointment Date')),
                ('duration_minutes', models.PositiveIntegerField(default=60, validators=[django.core.validators.MinValueValidator(15), django.core.validators.MaxValueValidator(480)], verbose_name='Duration (Minutes)')),
                ('end_time', models.DateTimeField(blank=True, help_text='Auto-calculated from start time and duration', null=True, verbose_name='End Time')),
                ('appointment_type', models.CharField(choices=[('CONSULTATION', 'Consultation'), ('ROUTINE_CHECKUP', 'Routine Checkup'), ('CLEANING', 'Cleaning'), ('EMERGENCY', 'Emergency'), ('TREATMENT', 'Treatment'), ('FOLLOW_UP', 'Follow-up'), ('SURGERY', 'Surgery'), ('ORTHODONTIC', 'Orthodontic'), ('RECALL', 'Recall'), ('NEW_PATIENT', 'New Patient'), ('TELEHEALTH', 'Telehealth'), ('BLOCK_TIME', 'Block Time')], default='CONSULTATION', max_length=20, verbose_name='Appointment Type')),
                ('status', models.CharField(choices=[('SCHEDULED', 'Scheduled'), ('CONFIRMED', 'Confirmed'), ('CHECKED_IN', 'Checked In'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('NO_SHOW', 'No Show'), ('CANCELLED', 'Cancelled'), ('RESCHEDULED', 'Rescheduled'), ('LATE_CANCELLATION', 'Late Cancellation')], default='SCHEDULED', max_length=30, verbose_name='Appointment Status')),
                ('chief_complaint', models.TextField(blank=True, help_text="Patient's primary concern or reason for visit", verbose_name='Chief Complaint')),
                ('procedure_codes', models.CharField(blank=True, help_text='Planned procedure codes (comma-separated)', max_length=200, verbose_name='Procedure Codes')),
                ('clinical_notes', models.TextField(blank=True, verbose_name='Clinical Notes')),
                ('priority', models.CharField(choices=[('EMERGENCY', 'Emergency'), ('URGENT', 'Urgent'), ('ROUTINE', 'Routine'), ('ELECTIVE', 'Elective')], default='ROUTINE', max_length=20, verbose_name='Priority')),
                ('is_recall_appointment', models.BooleanField(default=False, verbose_name='Recall Appointment')),
                ('recall_type', models.CharField(blank=True, choices=[('ROUTINE_CLEANING', 'Routine Cleaning'), ('PERIODONTAL_MAINTENANCE', 'Periodontal Maintenance'), ('ORTHODONTIC_ADJUSTMENT', 'Orthodontic Adjustment'), ('POST_TREATMENT', 'Post-treatment Check'), ('FLUORIDE_TREATMENT', 'Fluoride Treatment'), ('ORAL_CANCER_SCREENING', 'Oral Cancer Screening'), ('CUSTOM', 'Custom Recall')], max_length=30, verbose_name='Recall Type')),
                ('recall_interval_months', models.PositiveIntegerField(blank=True, null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(24)], verbose_name='Recall Interval (Months)')),
                ('treatment_room', models.CharField(blank=True, help_text='Assigned treatment room or operatory', max_length=50, verbose_name='Treatment Room')),
                ('special_equipment_needed', models.TextField(blank=True, verbose_name='Special Equipment Needed')),
                ('confirmation_sent', models.BooleanField(default=False, verbose_name='Confirmation Sent')),
                ('confirmation_method', models.CharField(blank=True, choices=[('EMAIL', 'Email'), ('SMS', 'SMS'), ('PHONE', 'Phone Call'), ('WHATSAPP', 'WhatsApp'), ('PATIENT_PORTAL', 'Patient Portal')], max_length=20, verbose_name='Confirmation Method')),
                ('confirmation_date', models.DateTimeField(blank=True, null=True, verbose_name='Confirmation Date')),
                ('reminder_sent', models.BooleanField(default=False, verbose_name='Reminder Sent')),
                ('reminder_date', models.DateTimeField(blank=True, null=True, verbose_name='Reminder Date')),
                ('check_in_time', models.DateTimeField(blank=True, null=True, verbose_name='Check-in Time')),
                ('actual_start_time', models.DateTimeField(blank=True, null=True, verbose_name='Actual Start Time')),
                ('actual_end_time', models.DateTimeField(blank=True, null=True, verbose_name='Actual End Time')),
                ('estimated_fee', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Estimated Fee')),
                ('insurance_pre_authorization', models.CharField(blank=True, max_length=100, verbose_name='Insurance Pre-authorization')),
                ('copay_collected', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Copay Collected')),
                ('special_instructions', models.TextField(blank=True, help_text='Special instructions for providers or staff', verbose_name='Special Instructions')),
                ('pre_medication_required', models.BooleanField(default=False, verbose_name='Pre-medication Required')),
                ('pre_medication_notes', models.TextField(blank=True, verbose_name='Pre-medication Notes')),
                ('wheelchair_accessible', models.BooleanField(default=False, verbose_name='Wheelchair Accessible')),
                ('interpreter_needed', models.BooleanField(default=False, verbose_name='Interpreter Needed')),
                ('interpreter_language', models.CharField(blank=True, max_length=50, verbose_name='Interpreter Language')),
                ('cancellation_reason', models.TextField(blank=True, verbose_name='Cancellation Reason')),
                ('cancelled_by', models.CharField(blank=True, choices=[('PATIENT', 'Patient'), ('PROVIDER', 'Provider'), ('STAFF', 'Staff'), ('SYSTEM', 'System')], max_length=20, verbose_name='Cancelled By')),
                ('cancellation_date', models.DateTimeField(blank=True, null=True, verbose_name='Cancellation Date')),
                ('no_show_fee', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='No Show Fee')),
                ('no_show_notes', models.TextField(blank=True, verbose_name='No Show Notes')),
                ('follow_up_needed', models.BooleanField(default=False, verbose_name='Follow-up Needed')),
                ('follow_up_interval_weeks', models.PositiveIntegerField(blank=True, null=True, verbose_name='Follow-up Interval (Weeks)')),
                ('patient_satisfaction_score', models.PositiveIntegerField(blank=True, help_text='1-5 scale satisfaction rating', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='Patient Satisfaction Score')),
                ('patient_feedback', models.TextField(blank=True, verbose_name='Patient Feedback')),
                ('assisting_providers', models.ManyToManyField(blank=True, related_name='assisting_appointments', to=settings.AUTH_USER_MODEL, verbose_name='Assisting Providers')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_appointments', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
                ('modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modified_appointments', to=settings.AUTH_USER_MODEL, verbose_name='Last Modified By')),
                ('primary_provider', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='primary_appointments', to=settings.AUTH_USER_MODEL, verbose_name='Primary Provider')),
                ('rescheduled_from', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='rescheduled_appointments', to='core.appointment', verbose_name='Rescheduled From')),
                ('ten', django_multitenant.fields.TenantForeignKey(editable=False, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='%(class)s_tenant', to='abstract.tenant')),
                ('updated_by', django_currentuser.db.models.fields.CurrentUserField(default=django_currentuser.middleware.get_current_authenticated_user, null=True, on_delete=django.db.models.deletion.CASCADE, on_update=True, related_name='%(app_label)s_%(class)s_updater', to=settings.AUTH_USER_MODEL, verbose_name='التعديل بواسطة')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='appointments', to='core.patient', verbose_name='Patient')),
                ('treatment_plan', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='appointments', to='core.treatmentplan', verbose_name='Treatment Plan')),
                ('treatment_plan_items', models.ManyToManyField(blank=True, help_text='Specific treatment items to be performed', related_name='appointments', to='core.treatmentplanitem', verbose_name='Treatment Plan Items')),
            ],
            options={
                'verbose_name': 'Appointment',
                'verbose_name_plural': 'Appointments',
                'ordering': ['appointment_date'],
                'abstract': False,
                'default_permissions': {},
                'base_manager_name': 'prefetch_manager',
            },
            bases=(django_multitenant.mixins.TenantModelMixin, django_lifecycle.mixins.LifecycleModelMixin, models.Model),
            managers=[
                ('objects', django.db.models.manager.Manager()),
                ('prefetch_manager', django.db.models.manager.Manager()),
            ],
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['username'], name='core_user_usernam_e8adca_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['phone_number'], name='core_user_phone_n_59ea9a_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['email'], name='core_user_email_38052c_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['user_type'], name='core_user_user_ty_e1e849_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['is_active'], name='core_user_is_acti_8c954f_idx'),
        ),
        migrations.AddIndex(
            model_name='patient',
            index=models.Index(fields=['clinic_id'], name='core_patien_clinic__ee5c6e_idx'),
        ),
        migrations.AddIndex(
            model_name='patient',
            index=models.Index(fields=['last_name', 'first_name'], name='core_patien_last_na_5d3812_idx'),
        ),
        migrations.AddIndex(
            model_name='patient',
            index=models.Index(fields=['primary_phone'], name='core_patien_primary_806b5c_idx'),
        ),
        migrations.AddIndex(
            model_name='patient',
            index=models.Index(fields=['email'], name='core_patien_email_e0bc4e_idx'),
        ),
        migrations.AddIndex(
            model_name='patient',
            index=models.Index(fields=['status'], name='core_patien_status_df853f_idx'),
        ),
        migrations.AddIndex(
            model_name='patient',
            index=models.Index(fields=['recall_due_date'], name='core_patien_recall__b95e51_idx'),
        ),
        migrations.AddIndex(
            model_name='diagnosis',
            index=models.Index(fields=['patient', '-diagnosis_date'], name='core_diagno_patient_199e9d_idx'),
        ),
        migrations.AddIndex(
            model_name='diagnosis',
            index=models.Index(fields=['diagnosis_category'], name='core_diagno_diagnos_c4ae84_idx'),
        ),
        migrations.AddIndex(
            model_name='diagnosis',
            index=models.Index(fields=['prognosis'], name='core_diagno_prognos_0c2903_idx'),
        ),
        migrations.AddIndex(
            model_name='diagnosis',
            index=models.Index(fields=['status'], name='core_diagno_status_b9df93_idx'),
        ),
        migrations.AddIndex(
            model_name='diagnosis',
            index=models.Index(fields=['case_sheet'], name='core_diagno_case_sh_212c52_idx'),
        ),
        migrations.AddIndex(
            model_name='casesheet',
            index=models.Index(fields=['patient', '-visit_date'], name='core_casesh_patient_de5f23_idx'),
        ),
        migrations.AddIndex(
            model_name='casesheet',
            index=models.Index(fields=['case_number'], name='core_casesh_case_nu_920512_idx'),
        ),
        migrations.AddIndex(
            model_name='casesheet',
            index=models.Index(fields=['status'], name='core_casesh_status_b168b5_idx'),
        ),
        migrations.AddIndex(
            model_name='casesheet',
            index=models.Index(fields=['case_type'], name='core_casesh_case_ty_18944c_idx'),
        ),
        migrations.AddIndex(
            model_name='casesheet',
            index=models.Index(fields=['visit_date'], name='core_casesh_visit_d_de92a2_idx'),
        ),
        migrations.AddConstraint(
            model_name='casesheet',
            constraint=models.UniqueConstraint(fields=('case_number', 'ten'), name='unique_case_number_per_tenant'),
        ),
        migrations.AddIndex(
            model_name='adjustment',
            index=models.Index(fields=['patient', '-adjustment_date'], name='core_adjust_patient_6566a8_idx'),
        ),
        migrations.AddIndex(
            model_name='adjustment',
            index=models.Index(fields=['adjustment_type'], name='core_adjust_adjustm_6e6f1c_idx'),
        ),
        migrations.AddIndex(
            model_name='adjustment',
            index=models.Index(fields=['status'], name='core_adjust_status_2aa60d_idx'),
        ),
        migrations.AddIndex(
            model_name='adjustment',
            index=models.Index(fields=['invoice'], name='core_adjust_invoice_6f089e_idx'),
        ),
        migrations.AddConstraint(
            model_name='adjustment',
            constraint=models.UniqueConstraint(fields=('adjustment_number', 'ten'), name='unique_adjustment_number_per_tenant'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['patient', '-payment_date'], name='core_paymen_patient_8b2a02_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['payment_number'], name='core_paymen_payment_63e43b_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['status'], name='core_paymen_status_8390cc_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['payment_method'], name='core_paymen_payment_91235d_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['invoice'], name='core_paymen_invoice_23d2a5_idx'),
        ),
        migrations.AddIndex(
            model_name='payment',
            index=models.Index(fields=['transaction_id'], name='core_paymen_transac_e8c2f7_idx'),
        ),
        migrations.AddConstraint(
            model_name='payment',
            constraint=models.UniqueConstraint(fields=('payment_number', 'ten'), name='unique_payment_number_per_tenant'),
        ),
        migrations.AddIndex(
            model_name='paymentapplication',
            index=models.Index(fields=['payment', '-application_date'], name='core_paymen_payment_09ea94_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentapplication',
            index=models.Index(fields=['invoice', '-application_date'], name='core_paymen_invoice_e8219e_idx'),
        ),
        migrations.AddIndex(
            model_name='tooth',
            index=models.Index(fields=['patient', 'fdi_number'], name='core_tooth_patient_b52de2_idx'),
        ),
        migrations.AddIndex(
            model_name='tooth',
            index=models.Index(fields=['fdi_number'], name='core_tooth_fdi_num_21f637_idx'),
        ),
        migrations.AddIndex(
            model_name='tooth',
            index=models.Index(fields=['status'], name='core_tooth_status_7c0e2a_idx'),
        ),
        migrations.AddIndex(
            model_name='tooth',
            index=models.Index(fields=['treatment_priority'], name='core_tooth_treatme_552d99_idx'),
        ),
        migrations.AddConstraint(
            model_name='tooth',
            constraint=models.UniqueConstraint(fields=('patient', 'fdi_number'), name='unique_tooth_per_patient'),
        ),
        migrations.AddIndex(
            model_name='treatmentplan',
            index=models.Index(fields=['patient', '-plan_date'], name='core_treatm_patient_70e46b_idx'),
        ),
        migrations.AddIndex(
            model_name='treatmentplan',
            index=models.Index(fields=['plan_number'], name='core_treatm_plan_nu_d6be0e_idx'),
        ),
        migrations.AddIndex(
            model_name='treatmentplan',
            index=models.Index(fields=['status'], name='core_treatm_status_637057_idx'),
        ),
        migrations.AddIndex(
            model_name='treatmentplan',
            index=models.Index(fields=['case_sheet'], name='core_treatm_case_sh_a2b78c_idx'),
        ),
        migrations.AddConstraint(
            model_name='treatmentplan',
            constraint=models.UniqueConstraint(fields=('plan_number', 'ten'), name='unique_plan_number_per_tenant'),
        ),
        migrations.AddIndex(
            model_name='invoice',
            index=models.Index(fields=['patient', '-invoice_date'], name='core_invoic_patient_f8850d_idx'),
        ),
        migrations.AddIndex(
            model_name='invoice',
            index=models.Index(fields=['invoice_number'], name='core_invoic_invoice_aef6bc_idx'),
        ),
        migrations.AddIndex(
            model_name='invoice',
            index=models.Index(fields=['status'], name='core_invoic_status_3d8780_idx'),
        ),
        migrations.AddIndex(
            model_name='invoice',
            index=models.Index(fields=['due_date'], name='core_invoic_due_dat_a17de6_idx'),
        ),
        migrations.AddConstraint(
            model_name='invoice',
            constraint=models.UniqueConstraint(fields=('invoice_number', 'ten'), name='unique_invoice_number_per_tenant'),
        ),
        migrations.AddIndex(
            model_name='treatmentplanitem',
            index=models.Index(fields=['treatment_plan', 'item_order'], name='core_treatm_treatme_376339_idx'),
        ),
        migrations.AddIndex(
            model_name='treatmentplanitem',
            index=models.Index(fields=['status'], name='core_treatm_status_23aec8_idx'),
        ),
        migrations.AddIndex(
            model_name='treatmentplanitem',
            index=models.Index(fields=['treatment_phase'], name='core_treatm_treatme_4cfda1_idx'),
        ),
        migrations.AddIndex(
            model_name='treatmentplanitem',
            index=models.Index(fields=['scheduled_date'], name='core_treatm_schedul_124bcb_idx'),
        ),
        migrations.AddIndex(
            model_name='appointment',
            index=models.Index(fields=['patient', 'appointment_date'], name='core_appoin_patient_2cc7c6_idx'),
        ),
        migrations.AddIndex(
            model_name='appointment',
            index=models.Index(fields=['appointment_date'], name='core_appoin_appoint_5d7cff_idx'),
        ),
        migrations.AddIndex(
            model_name='appointment',
            index=models.Index(fields=['primary_provider', 'appointment_date'], name='core_appoin_primary_ec1e0a_idx'),
        ),
        migrations.AddIndex(
            model_name='appointment',
            index=models.Index(fields=['status'], name='core_appoin_status_e978b8_idx'),
        ),
        migrations.AddIndex(
            model_name='appointment',
            index=models.Index(fields=['appointment_type'], name='core_appoin_appoint_c1b500_idx'),
        ),
        migrations.AddIndex(
            model_name='appointment',
            index=models.Index(fields=['is_recall_appointment'], name='core_appoin_is_reca_7d8694_idx'),
        ),
        migrations.AddIndex(
            model_name='appointment',
            index=models.Index(fields=['treatment_room', 'appointment_date'], name='core_appoin_treatme_18fbe5_idx'),
        ),
        migrations.AddConstraint(
            model_name='appointment',
            constraint=models.UniqueConstraint(fields=('appointment_number', 'ten'), name='unique_appointment_number_per_tenant'),
        ),
    ]
