# Generated by Django 5.2.4 on 2025-07-11 23:24

import core.models.user
import django.db.models.manager
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='DentistUser',
            fields=[
            ],
            options={
                'verbose_name': 'Dentist User',
                'verbose_name_plural': 'Dentist Users',
                'abstract': False,
                'proxy': True,
                'default_permissions': {},
                'base_manager_name': 'prefetch_manager',
                'indexes': [],
                'constraints': [],
            },
            bases=('core.user',),
            managers=[
                ('objects', core.models.user.DentistUserManager()),
                ('prefetch_manager', django.db.models.manager.Manager()),
            ],
        ),
        migrations.AlterField(
            model_name='user',
            name='user_type',
            field=models.CharField(choices=[('admin', 'Admin'), ('receptionist', 'Receptionist'), ('patient', 'Patient'), ('dentist', 'Dentist')], default='patient', max_length=20, verbose_name='User Type'),
        ),
    ]
