# Django Inertia Query Parameters Middleware

This middleware automatically exposes URL query parameters to your Inertia.js application using Inertia's `share` feature, making them globally available as props across all components without manual intervention.

## Features

- 🔄 **Automatic Sharing**: Query parameters are automatically shared with all Inertia components
- 🚀 **Lazy Evaluation**: Parameters are evaluated lazily for optimal performance
- 🛡️ **Security**: Built-in parameter filtering and length limits
- 📊 **Multi-value Support**: Handles parameters with multiple values (arrays)
- ⚙️ **Configurable**: Extensive configuration options via Django settings
- 🔍 **Logging**: Comprehensive logging for debugging and monitoring

## Installation

1. The middleware is already installed in this project at `core.middlewares.params.QueryParamsShareMiddleware`

2. Add it to your `MIDDLEWARE` setting in `settings.py` (already configured):
```python
MIDDLEWARE = [
    # ... other middleware
    'inertia.middleware.InertiaMiddleware',
    'core.middlewares.params.QueryParamsShareMiddleware',  # Add after InertiaMiddleware
]
```

## Usage

Once installed, query parameters are automatically available in all your Inertia components:

### In Svelte Components

```svelte
<script lang="ts">
  import { page } from "@inertiajs/svelte";
  
  // Access shared query parameters
  $: queryParams = $page.props.query_params || {};
  $: currentUrl = $page.props.current_url || '';
  $: currentPath = $page.props.current_path || '';
  $: queryString = $page.props.query_string || '';
</script>

<div>
  <h2>Current URL: {currentUrl}</h2>
  
  {#if Object.keys(queryParams).length > 0}
    <h3>Query Parameters:</h3>
    <ul>
      {#each Object.entries(queryParams) as [key, value]}
        <li>
          <strong>{key}:</strong> 
          {Array.isArray(value) ? value.join(', ') : value}
        </li>
      {/each}
    </ul>
  {:else}
    <p>No query parameters</p>
  {/if}
</div>
```

### In Vue Components

```vue
<template>
  <div>
    <h2>Current URL: {{ $page.props.current_url }}</h2>
    
    <div v-if="Object.keys($page.props.query_params || {}).length > 0">
      <h3>Query Parameters:</h3>
      <ul>
        <li v-for="[key, value] in Object.entries($page.props.query_params)" :key="key">
          <strong>{{ key }}:</strong> 
          {{ Array.isArray(value) ? value.join(', ') : value }}
        </li>
      </ul>
    </div>
    <p v-else>No query parameters</p>
  </div>
</template>
```

### In React Components

```jsx
import { usePage } from '@inertiajs/react';

export default function MyComponent() {
  const { props } = usePage();
  const queryParams = props.query_params || {};
  const currentUrl = props.current_url || '';
  
  return (
    <div>
      <h2>Current URL: {currentUrl}</h2>
      
      {Object.keys(queryParams).length > 0 ? (
        <div>
          <h3>Query Parameters:</h3>
          <ul>
            {Object.entries(queryParams).map(([key, value]) => (
              <li key={key}>
                <strong>{key}:</strong> {
                  Array.isArray(value) ? value.join(', ') : value
                }
              </li>
            ))}
          </ul>
        </div>
      ) : (
        <p>No query parameters</p>
      )}
    </div>
  );
}
```

## Configuration

Configure the middleware behavior in your `settings.py`:

```python
# Query Parameters Sharing Configuration
INERTIA_SHARE_QUERY_PARAMS = True  # Enable/disable query params sharing

INERTIA_QUERY_PARAMS_EXCLUDE = [    # Parameters to exclude from sharing
    'csrfmiddlewaretoken',          # Django CSRF token
    '_token',                       # Alternative CSRF token name
    'page',                         # Often handled separately in pagination
    'utm_source',                   # Example: exclude tracking parameters
    'utm_medium',
    'utm_campaign',
]

INERTIA_MAX_QUERY_PARAM_LENGTH = 1000  # Maximum length for query parameter values
```

### Configuration Options

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `INERTIA_SHARE_QUERY_PARAMS` | `bool` | `True` | Enable/disable the middleware |
| `INERTIA_QUERY_PARAMS_EXCLUDE` | `list` | `['csrfmiddlewaretoken', '_token', 'page']` | Query parameters to exclude |
| `INERTIA_MAX_QUERY_PARAM_LENGTH` | `int` | `1000` | Maximum allowed length for parameter values |

## Shared Props

The middleware automatically shares these props with all Inertia components:

- **`query_params`**: Object containing all query parameters (excluding filtered ones)
- **`current_url`**: The complete current URL
- **`current_path`**: The current path without query string
- **`query_string`**: The raw query string

## Examples

### URL: `http://localhost:8000/?name=John&age=25&tags=developer&tags=svelte`

**Shared props:**
```javascript
{
  query_params: {
    name: "John",
    age: "25",
    tags: ["developer", "svelte"]  // Multiple values become arrays
  },
  current_url: "http://localhost:8000/?name=John&age=25&tags=developer&tags=svelte",
  current_path: "/",
  query_string: "name=John&age=25&tags=developer&tags=svelte"
}
```

### URL: `http://localhost:8000/users/?search=admin&status=active&page=2`

**Shared props (with default exclusions):**
```javascript
{
  query_params: {
    search: "admin",
    status: "active"
    // Note: 'page' is excluded by default
  },
  current_url: "http://localhost:8000/users/?search=admin&status=active&page=2",
  current_path: "/users/",
  query_string: "search=admin&status=active&page=2"
}
```

## Security Considerations

- **Parameter Filtering**: Sensitive parameters like CSRF tokens are automatically excluded
- **Length Limits**: Parameter values are truncated if they exceed the configured maximum length
- **Logging**: Parameter access is logged for security monitoring
- **Sanitization**: All parameter values are properly sanitized

## Debugging

Enable logging to debug middleware behavior:

```python
# settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'core.middlewares.params': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': False,
        },
    },
}
```

## Performance Notes

- **Lazy Evaluation**: All shared props use lazy evaluation (`lambda` functions) to avoid unnecessary computation
- **Request Filtering**: The middleware only processes relevant requests (Inertia or HTML requests)
- **Minimal Overhead**: Query parameter processing has minimal performance impact

## Alternative Implementation

For older Django versions or `MIDDLEWARE_CLASSES` compatibility, use `QueryParamsShareMiddlewareMixin`:

```python
# settings.py
MIDDLEWARE = [
    # ... other middleware
    'core.middlewares.params.QueryParamsShareMiddlewareMixin',
]
```

## Troubleshooting

### Query parameters not appearing in components

1. **Check middleware order**: Ensure `QueryParamsShareMiddleware` comes after `InertiaMiddleware`
2. **Verify settings**: Check that `INERTIA_SHARE_QUERY_PARAMS = True`
3. **Check exclusions**: Verify your parameter isn't in `INERTIA_QUERY_PARAMS_EXCLUDE`
4. **Enable logging**: Add debug logging to see what's happening

### Performance issues

1. **Check parameter length**: Very long parameters might be causing issues
2. **Review exclusions**: Add frequently-used but unnecessary parameters to exclusions
3. **Monitor logs**: Check for warning messages about truncated parameters

## Contributing

To extend or modify the middleware:

1. The main implementation is in `core/middlewares/params.py`
2. Add tests in the appropriate test directory
3. Update this documentation for any new features
4. Follow the existing code style and patterns

## License

This middleware is part of the Django-Inertia-Svelte project and follows the same license terms. 