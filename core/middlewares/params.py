from inertia import share
from django.conf import settings
from django.utils.deprecation import MiddlewareMixin
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class QueryParamsShareMiddleware:
    """
    Django middleware that uses Inertia's share feature to expose query parameters
    to the Inertia app as globally available props.
    
    This middleware makes query parameters available across all Inertia components
    without needing to manually pass them in each view.
    """
    
    def __init__(self, get_response):
        """
        Initialize the middleware with the get_response callable.
        
        Args:
            get_response: The next middleware or view in the chain
        """
        self.get_response = get_response
        # One-time configuration and initialization
        self.enabled = getattr(settings, 'INERTIA_SHARE_QUERY_PARAMS', True)
        self.excluded_params = set(getattr(settings, 'INERTIA_QUERY_PARAMS_EXCLUDE', [
            'csrfmiddlewaretoken',  # Django CSRF token
            '_token',               # Alternative CSRF token name
            'page',                 # Common pagination param that might be handled separately
        ]))
        self.max_param_length = getattr(settings, 'INERTIA_MAX_QUERY_PARAM_LENGTH', 1000)
        
        logger.info(
            f"QueryParamsShareMiddleware initialized - "
            f"enabled: {self.enabled}, "
            f"excluded_params: {self.excluded_params}, "
            f"max_param_length: {self.max_param_length}"
        )

    def __call__(self, request):
        """
        Process the request and share query parameters with Inertia.
        
        Args:
            request: The HTTP request object
            
        Returns:
            The HTTP response
        """
        # Code executed for each request before the view is called
        if self.enabled and hasattr(request, 'META') and self._is_inertia_request(request):
            query_params = self._extract_query_params(request)
            if query_params:
                # Share the query parameters with Inertia using lazy evaluation
                share(
                    request,
                    query_params=lambda: query_params,
                    current_url=lambda: request.build_absolute_uri(),
                    current_path=lambda: request.path,
                    query_string=lambda: request.META.get('QUERY_STRING', ''),
                )
                
                logger.debug(f"Shared query params: {list(query_params.keys())}")

        response = self.get_response(request)

        # Code executed for each request/response after the view is called
        return response

    def _is_inertia_request(self, request) -> bool:
        """
        Check if this is an Inertia request or a regular request that might render Inertia.
        
        Args:
            request: The HTTP request object
            
        Returns:
            bool: True if this is an Inertia-related request
        """
        # Check for Inertia headers
        is_inertia = request.headers.get('X-Inertia', '').lower() == 'true'
        
        # Also share for regular requests since they might render Inertia components
        is_html_request = (
            'text/html' in request.headers.get('Accept', '') or
            not request.headers.get('X-Requested-With')  # Regular browser requests
        )
        
        return is_inertia or is_html_request

    def _extract_query_params(self, request) -> Dict[str, Any]:
        """
        Extract and sanitize query parameters from the request.
        
        Args:
            request: The HTTP request object
            
        Returns:
            Dict[str, Any]: Cleaned query parameters
        """
        query_params = {}
        
        if not hasattr(request, 'GET'):
            return query_params
            
        for key, value in request.GET.items():
            # Skip excluded parameters
            if key.lower() in self.excluded_params:
                continue
                
            # Sanitize parameter length
            if len(str(value)) > self.max_param_length:
                logger.warning(
                    f"Query parameter '{key}' exceeded max length "
                    f"({len(str(value))} > {self.max_param_length}), truncating"
                )
                value = str(value)[:self.max_param_length]
            
            # Handle multiple values for the same parameter
            all_values = request.GET.getlist(key)
            if len(all_values) > 1:
                # Multiple values: return as array
                query_params[key] = [
                    val[:self.max_param_length] if len(str(val)) > self.max_param_length else val
                    for val in all_values
                ]
            else:
                # Single value: return as string/value
                query_params[key] = value
                
        return query_params


# Alternative implementation using MiddlewareMixin for older Django compatibility
class QueryParamsShareMiddlewareMixin(MiddlewareMixin):
    """
    Alternative implementation using MiddlewareMixin for backward compatibility.
    Use this if you need compatibility with older Django versions or MIDDLEWARE_CLASSES.
    """
    
    def __init__(self, get_response=None):
        super().__init__(get_response)
        self.enabled = getattr(settings, 'INERTIA_SHARE_QUERY_PARAMS', True)
        self.excluded_params = set(getattr(settings, 'INERTIA_QUERY_PARAMS_EXCLUDE', [
            'csrfmiddlewaretoken',
            '_token',
            'page',
        ]))
        self.max_param_length = getattr(settings, 'INERTIA_MAX_QUERY_PARAM_LENGTH', 1000)

    def process_request(self, request):
        """
        Process the request and share query parameters with Inertia.
        
        Args:
            request: The HTTP request object
        """
        if self.enabled and self._is_inertia_request(request):
            query_params = self._extract_query_params(request)
            if query_params:
                share(
                    request,
                    query_params=lambda: query_params,
                    current_url=lambda: request.build_absolute_uri(),
                    current_path=lambda: request.path,
                    query_string=lambda: request.META.get('QUERY_STRING', ''),
                )

    def _is_inertia_request(self, request) -> bool:
        """Check if this is an Inertia-related request."""
        is_inertia = request.headers.get('X-Inertia', '').lower() == 'true'
        is_html_request = 'text/html' in request.headers.get('Accept', '')
        return is_inertia or is_html_request

    def _extract_query_params(self, request) -> Dict[str, Any]:
        """Extract and sanitize query parameters from the request."""
        query_params = {}
        
        if not hasattr(request, 'GET'):
            return query_params
            
        for key, value in request.GET.items():
            if key.lower() in self.excluded_params:
                continue
                
            if len(str(value)) > self.max_param_length:
                value = str(value)[:self.max_param_length]
            
            all_values = request.GET.getlist(key)
            if len(all_values) > 1:
                query_params[key] = [
                    val[:self.max_param_length] if len(str(val)) > self.max_param_length else val
                    for val in all_values
                ]
            else:
                query_params[key] = value
                
        return query_params

