# How to Use the Multitenancy Models in `@/abstract`

This guide explains how to use the multitenancy features provided by the `abstract` app. The implementation is built on top of the `django-multitenant` library to provide row-level data isolation based on the current tenant.

## Core Concepts

Our multitenancy architecture revolves around a few key components in the `abstract` app.

### 1. The `Tenant` Model

-   **Path:** `abstract/models/tenant.py`
-   **Model:** `abstract.models.Tenant`

This is the central model that represents a single tenant in the system. All tenant-specific data will be linked to an instance of this model.

### 2. The `IntEntity` Abstract Base Model

-   **Path:** `abstract/models/core.py`
-   **Model:** `abstract.models.IntEntity`

This is an abstract model that your models should inherit from to become tenant-aware. It provides the following out-of-the-box:

-   A `ten` field which is a `TenantForeignKey` to the `Tenant` model. This links the object to a specific tenant.
-   A manager (`IntEntityManager`) that inherits from `django-multitenant`'s `TenantManagerMixin`. This automatically filters all querysets to only include objects belonging to the currently active tenant.
-   Audit logging and other base fields (`id`, `created_at`, etc.).

```python
# abstract/models/core.py

class IntEntity(TenantModelMixin, LifecycleModel, AuditMixin, TenantMixin):
    objects = IntEntityManager()
    
    class Meta(auto_prefetch.Model.Meta):
        abstract = True
        # ...

    id = models.UUIDField(default=uuid.uuid4, primary_key=True, editable=False)
    # ... other fields
```

### 3. The `MultitenantMiddleware`

-   **Path:** `abstract/middleware/middleware.py`
-   **Middleware:** `abstract.middleware.middleware.MultitenantMiddleware`

This middleware is responsible for setting the current tenant for each request. It inspects the logged-in `request.user`, finds their associated tenant via the `user.ten` attribute, and sets it in a thread-local variable for `django-multitenant` to use.

## How to Implement Multitenancy

Follow these steps to make your app's models tenant-aware.

### Step 1: Make Your Models Tenant-Aware

To make a model's data specific to a tenant, simply inherit from `abstract.models.IntEntity`.

**Example:** Let's create a `Project` model.

```python
# your_app/models.py
from abstract.models import IntEntity
from django.db import models

class Project(IntEntity):
    name = models.CharField(max_length=100)
    description = models.TextField()

    def __str__(self):
        return self.name
```

That's it! Now, `Project` objects are tied to tenants. The `ten` field and other fields from `IntEntity` are automatically added. The model manager will automatically handle data isolation.

### Step 2: Configure the User Model

The `MultitenantMiddleware` needs to know which tenant the current user belongs to. To enable this, you must add a `ForeignKey` to the `Tenant` model on your custom user model.

**Important:** The field **must** be named `ten`.

**Example:** If you have a custom user model in a `users` app:

```python
# users/models.py
from django.contrib.auth.models import AbstractUser
from django.db import models
from abstract.models import Tenant

class CustomUser(AbstractUser):
    # ... your other fields
    ten = models.ForeignKey(
        Tenant, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='users'
    )
```

### Step 3: Configure Middleware in `settings.py`

You need to add the `MultitenantMiddleware` to your `MIDDLEWARE` setting in `config/settings.py`. It should be placed after Django's `AuthenticationMiddleware`.

```python
# config/settings.py

MIDDLEWARE = [
    # ... other middleware
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'abstract.middleware.middleware.MultitenantMiddleware', # <-- Add this
    # ... other middleware
]
```

### Step 4: Querying Tenant-Aware Models

Once everything is set up, `django-multitenant` handles the data isolation automatically. When you query a model that inherits from `IntEntity`, the queryset will be filtered for the current tenant set by the middleware.

**Example View:**

```python
# your_app/views.py
from django.views.generic import ListView
from .models import Project

class ProjectListView(ListView):
    model = Project
    template_name = 'projects/list.html'

    def get_queryset(self):
        # This will automatically return Project.objects.filter(ten=request.tenant)
        # You don't need to add the filter yourself!
        qs = super().get_queryset() 
        return qs
```

A user from "Tenant A" will only see projects belonging to "Tenant A". A user from "Tenant B" will only see their own projects.

### Superuser Access

Superusers are generally considered system-wide administrators and are not tied to a specific tenant. The middleware allows them to operate without being associated with a tenant. Querysets for tenant-aware models will not be filtered when the current user is a superuser and no tenant is explicitly set.

## Practical Example: `TenantGroup`

The `abstract.models.TenantGroup` model is a working example of a tenant-aware model. It's used to create permission groups that are specific to each tenant, demonstrating how you can build tenant-isolated features on top of this framework.

```python
# abstract/models/tenant_group.py
from django.db.models import UniqueConstraint

class TenantGroup(IntEntity):
    group_name = models.CharField(verbose_name=_('الاسم'), max_length=255)
    group = models.ForeignKey(Group, verbose_name=_('المجموعة'), on_delete=models.CASCADE, null=True,
                              blank=True, related_name='tenant_groups')
    # ...
    class Meta:
        # ...
        constraints = [
            UniqueConstraint(fields=['group_name', 'ten'], name='unique_group_name_tenant'),
        ]
```
This constraint ensures that a group name is unique *within* a tenant, but different tenants can have groups with the same name. 