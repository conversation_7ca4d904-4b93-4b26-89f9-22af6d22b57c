# How to Build a Django + Inertia + Svelte Project

This guide provides a comprehensive walkthrough on how to set up a new project using Django, Inertia.js, and Svelte from scratch, based on the configuration of this project.

## 1. Backend Setup (Django)

1.  **Create a Django Project and App:**

    ```bash
    django-admin startproject config .
    python manage.py startapp core
    ```

2.  **Install Required Django Packages:**

    Install the necessary packages as defined in `pyproject.toml`.

    ```bash
    pip install django django-vite inertia-django
    ```

3.  **Configure `settings.py`:**

    In `config/settings.py`, make the following additions:

    -   **`INSTALLED_APPS`:**

        ```python
        INSTALLED_APPS = [
            ...
            'django_vite',
            'inertia',
            'core',
        ]
        ```

    -   **`MIDDLEWARE`:**

        ```python
        MIDDLEWARE = [
            ...
            'inertia.middleware.InertiaMiddleware',
        ]
        ```

    -   **`TEMPLATES`:**

        ```python
        TEMPLATES = [
            {
                'BACKEND': 'django.template.backends.django.DjangoTemplates',
                'DIRS': [BASE_DIR / 'templates'],
                'APP_DIRS': True,
                ...
            },
        ]
        ```

    -   **Static Files and Vite Configuration:**

        ```python
        STATIC_URL = 'static/'
        STATIC_ROOT = BASE_DIR / "static"
        STATICFILES_DIRS = [
            BASE_DIR / "web" / "dist"
        ]

        DJANGO_VITE_DEV_MODE = True
        DJANGO_VITE_ASSETS_PATH = BASE_DIR / "web" / "dist"
        DJANGO_VITE_MANIFEST_PATH = DJANGO_VITE_ASSETS_PATH / "manifest.json"
        DJANGO_VITE_DEV_SERVER_PORT = 5173
        STATICFILES_DIRS.append(DJANGO_VITE_ASSETS_PATH)
        ```

    -   **Inertia Configuration:**

        ```python
        INERTIA_LAYOUT = "base.html"
        ```

4.  **Create the Base Template:**

    Create `templates/base.html`:

    ```html
    {% load django_vite %}
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">

        <script type="module" src="http://localhost:5173/@vite/client"></script>
        <script type="module" src="http://localhost:5173/src/app.js"></script>
        <link rel="stylesheet" href="http://localhost:5173/src/app.css">

        <title>Django Svelte Template</title>
    </head>

    <body>
    {% block inertia %}{% endblock %}
    </body>
    </html>
    ```

## 2. Frontend Setup (Svelte)

1.  **Create a Svelte Project:**

    In the project root, create a `web` directory for the frontend.

    ```bash
    npm create vite@latest web -- --template svelte
    cd web
    ```

2.  **Install Frontend Dependencies:**

    Install the dependencies from `package.json`.

    ```bash
    pnpm install @inertiajs/svelte svelte
    ```

3.  **Configure `vite.config.js`:**

    ```javascript
    import tailwindcss from "@tailwindcss/vite";
    import { defineConfig } from "vite";
    import { svelte } from "@sveltejs/vite-plugin-svelte";
    import path from "path";

    export default defineConfig({
        plugins: [tailwindcss(), svelte()],
        server: { port: 5173, host: true },
        resolve: { alias: { $lib: path.resolve("./src/lib") } }
    });
    ```

4.  **Configure `src/app.js`:**

    ```javascript
    import { createInertiaApp } from '@inertiajs/svelte'
    import { mount } from 'svelte'
    import DefaultLayout from './layouts/DefaultLayout.svelte'

    createInertiaApp({
      id: 'app',
      resolve: name => {
        const pages = import.meta.glob('./pages/**/*.svelte', { eager: true })
        let page = pages[`./pages/${name}.svelte`]
        return {
          default: page.default,
          layout: page.layout || DefaultLayout,
        }
      },
      setup({ el, App, props }) {
        mount(App, { target: el, props })
      },
    })
    ```

## 3. Integration and Running the Project

1.  **Create a Django View:**

    In `core/views.py`:

    ```python
    from inertia import inertia

    @inertia('Index')
    def index_view(request):
        return {
                "message": "Hello from the backend",
                "abbas": {"name": "Abbas", "age": 20, "city": "Tehran"},
            }
    ```

2.  **Create URL Routes:**

    -   In `config/urls.py`:

        ```python
        from django.contrib import admin
        from django.urls import path, include

        urlpatterns = [
            path('admin/', admin.site.urls),
            path('', include("core.urls"))
        ]
        ```

    -   In `core/urls.py`:

        ```python
        from django.urls import path
        from .views import index_view

        urlpatterns = [
            path("", index_view, name="index"),
        ]
        ```

3.  **Run the Development Servers:**

    -   **Django:** `python manage.py runserver`
    -   **Vite:** `cd web && pnpm run dev`

Now, visiting `http://127.0.0.1:8000` will render your Svelte application within the Django template.
