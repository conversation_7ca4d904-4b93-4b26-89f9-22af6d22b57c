# Product Requirements Document (PRD)  
_DnD – Dental & Diagnosis Clinic Management Platform_

Digital transformation is rapidly reshaping dentistry. Studies show that modern practice-management suites reduce administrative time by 20 hours per week and increase collections by 29% on average. DnD aims to deliver these gains in a lightweight, browser-based solution purpose-built for small-to-midsize clinics.  

## Executive Summary
DnD is a responsive web application that unifies patient administration, tooth-level clinical charting, diagnostic tracking, and revenue workflows behind a role-based security layer. The primary design principle—_"zero wasted clicks"_—drives aggressive use of pre-populated lists, autocomplete, and context-aware defaults to keep data entry below 30 seconds per case update. Core modules are Patient Management, Case Sheet & Tooth Presentation, Diagnosis & Prognosis, Invoicing & Financials, and Workflow & Notifications.  

## 1. Product Vision & Goals

| Goal | Success Metric | Supporting Sources |
|------|---------------|--------------------|
| Reduce chair-side charting time | ≤45 s to record a new finding | Interactive odontograms cut charting time by 3.5× |
| Improve revenue cycle | 95% of invoices paid within 30 days | Automated reminders & text-to-pay lift collections 40% |
| Scale with clinic growth | Seamless performance at 100k patients | Cloud PM systems show linear scaling |

## 3. Functional Requirements

### 3.1 Patient Management

**Key Features**  
1. Unified patient profile: demographics, contacts, medical & dental histories.  
2. Advanced search: name, ID, phone, diagnosis, treatment status, balance.  

**User Stories & Acceptance Criteria**

| ID | User Story | Acceptance Test |
|----|------------|-----------------|
| PM-01 | As a receptionist, I filter patients overdue for follow-ups. | List returns correct subset in ≤2 s with dynamic chips. |
| PM-02 | As a clinician, I open medical alerts before charting. | Alert badge visible and modal opens on click. |

### 3.2 Case Sheet & Tooth Presentation  

**Design Principles**  
* Full FDI two-digit chart with adult/child toggle.  
* Click on tooth ⇒ context panel (diagnosis, notes, images).  
* Split view: odontogram left, timeline right.

**File Handling**  
* Drag-and-drop upload; accepted formats: JPEG, PNG, PDF, DOCX.  
* Inline viewer for X-ray DICOM via image viewer.

### 3.3 Diagnosis & Prognosis
* Pre-loaded drop-downs of 250+ ICD-11 dental codes.  
* Prognosis scale: Good / Fair / Poor / Hopeless, aligned with periodontal literature.  
* AI suggestion engine (phase II) referencing past similar cases.  

### 3.4 Invoicing & Financials
* One-click invoice from accepted treatment plan.  
* Support for discounts, promo codes, membership tiers.  
* Ledger view: charges, payments, adjustments, loyalty balance.  

### 3.5 Workflow & Notifications
* Role-based dashboards: pending charts, unpaid invoices, expiring recalls.  
* Automated reminders: appointment, payment, recare; email/SMS/WebPush.  
* Task lists & huddle board for multi-provider clinics.

## 4. Non-Functional Requirements

| Category | Spec | Rationale & Citations |
|----------|------|-----------------------|
| Performance | 95th-percentile page load <1.2 s at 4G | Mobile users dominate patient portals |
| Scalability | Horizontally scalable stateless API; tested to 500 concurrent sessions | Cloud PMs scale linearly |
| Security | AES-256-GCM at rest, TLS 1.3 in transit | HIPAA encryption best practice |
| Availability | 99.9% uptime; multi-AZ deployment with hourly snapshots | Healthcare data loss is critical |

**Audit Logging:** All create/update/delete events, logins, permissions changes; 6-year retention.

## 5. User Experience & UI Guidelines

| Area | UX Standard |
|------|-------------|
| Navigation | Primary sidebar + breadcrumb; max two clicks to core tasks. |
| Data Entry | Autocomplete everywhere; default focus & tab order for keyboard users. |
| Indicators | Color badges: red = overdue, amber = draft, green = complete. |
| Responsiveness | Fluid grid; breakpoint @ 768 px transitions sidebar to drawer. |

Wireframes included in Appendix (fig. A) and clickable Figma prototype link (internal).

## 6. Technical Architecture

* **FullStack**: Django (Python) with Inertia.js (JS) frontend and Svelte (JS) components.
* **Database**: SQLITE database.  
* **Authentication**: username/password with RBAC scopes (admin, dentist, staff, read-only).  

## 7. Detailed User Flows

### 7.1 New Patient Intake (Data-Entry Persona)
1. Click "Add Patient" → modal opens with demographic form pre-filled by postcode API.  
2. On save, system auto-generates unique clinic ID and placeholder odontogram.  
3. Intake screen auto-navigates to medical history; autocomplete medications list.  
4. "Finish Intake" submits and opens patient dashboard.

### 7.2 Chair-Side Charting (Dentist Persona)
1. Select today's appointment → opens case sheet.  
2. Tap tooth 26; context panel shows previous findings.  
3. Choose "MOD composite" from quick-codes; duration auto-defaults 45 min.  
4. Hit "Save & Plan" → treatment plan queue.  
5. Dentist clicks "Present Plan"; patient approves on tablet via e-signature.  
6. Invoice auto-generates and is queued for front desk checkout.

### 7.3 Payment & Loyalty (Front Desk Persona)
1. Invoice opens; apply 10% (configurable) promo if loyalty credits ≥100 (configurable).  
2. Click "Text-to-Pay"; patient receives the invoice via whatsapp.  

## 8. Acceptance Test Matrix (Excerpt)

| Feature | Scenario | Given | When | Then |
|---------|----------|-------|------|------|
| Odontogram click | Tooth selection | User = dentist; case open | Click tooth 11 | Tooth 11 detail panel slides in |
| Invoice promo | Loyalty credit | Patient has ≥100 pts | Apply promocode | Discount line item appears; total recalculated |
| Audit log | Record edit | User edits diagnosis | Save | Entry logged with before/after JSON |

## 9. Risks & Mitigations

| Risk | Probability | Impact | Mitigation |
|------|-------------|--------|------------|
| Browser DICOM viewer performance | Medium | High | Progressive loading; fallback PNG preview |
| Regulatory drift (e-invoice mandates) | Medium | Medium | Configurable fiscal plugin layer |
| Rural clinics with poor internet | Low | Medium | PWA offline mode; queued sync |

## 10. Roadmap & Release Plan

| Milestone | Scope | Target |
|-----------|-------|--------|
| Alpha (M3) | Patient core, odontogram read-only | Dev clinics |
| Beta (M6) | Full CRUD, invoicing, audit log | 5 pilot sites |
| GA (M9) | Notifications, loyalty, export, SLA 99.9% | Public launch |

## Conclusion

DnD consolidates best-practice workflows—interactive charting, role-based security, mobile-first responsiveness, and automated revenue tools—into a single, cloud-native platform. By emphasizing time-saving UX patterns and rigorous compliance, the product positions clinics to deliver safer, faster, and more profitable care.  

### End of Document

**Document Information:**
- Document Title: DnD (Dental & Diagnosis) Product Requirements Document
- Version: 1.0
- Date: July 7, 2025
- Total Pages: Comprehensive PRD covering all functional and non-functional requirements
- Classification: Internal Development Document