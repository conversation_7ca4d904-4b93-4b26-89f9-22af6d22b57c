import auto_prefetch
from django.db import models
from django.utils.translation import gettext_lazy as _


class MunPermissionsChoices(models.TextChoices):
    pass

class MunPermissions(auto_prefetch.Model):
    class Meta(auto_prefetch.Model.Meta):
        db_table = "mun_permissions"
        verbose_name = _('صلاحية البلدية')
        verbose_name_plural = _('صلاحيات البلدية')

        default_permissions = (
        )

        permissions = [
            (perm.value, perm.label) for perm in MunPermissionsChoices
        ]
