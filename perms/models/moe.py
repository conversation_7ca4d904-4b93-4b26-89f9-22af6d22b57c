import auto_prefetch
from django.db import models
from django.utils.translation import gettext_lazy as _


class MoePermissionsChoices(models.TextChoices):
    pass


class MoePermissions(auto_prefetch.Model):
    class Meta(auto_prefetch.Model.Meta):
        db_table = "moe_permissions"
        verbose_name = _('صلاحية وزارة الكهرباء')
        verbose_name_plural = _('صلاحيات وزارة الكهرباء')

        default_permissions = (
        )

        permissions = [
            (perm.value, perm.label) for perm in MoePermissionsChoices
        ]
