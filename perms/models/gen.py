import auto_prefetch
from django.db import models
from django.utils.translation import gettext_lazy as _


class GenPermissionsChoices(models.TextChoices):
    pass

class GenPermissions(auto_prefetch.Model):
    class Meta(auto_prefetch.Model.Meta):
        db_table = "gen_permissions"
        verbose_name = _('صلاحية المولدات')
        verbose_name_plural = _('صلاحيات المولدات')

        default_permissions = (
        )

        permissions = [
            (perm.value, perm.label) for perm in GenPermissionsChoices
        ]
