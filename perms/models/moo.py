import auto_prefetch
from django.db import models
from django.utils.translation import gettext_lazy as _


class MooPermissionsChoices(models.TextChoices):
    pass

class MooPermissions(auto_prefetch.Model):
    class Meta(auto_prefetch.Model.Meta):
        db_table = "moo_permissions"
        verbose_name = _('صلاحية وزارة النفط')
        verbose_name_plural = _('صلاحيات وزارة النفط')

        default_permissions = (
        )
        permissions = [
            (perm.value, perm.label) for perm in MooPermissionsChoices
        ]
