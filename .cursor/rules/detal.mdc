---
alwaysApply: true
---
FIRST OF ALL:

Remember, my tech stack is allways as follows:

Backend:
- Django 5+

Frontend:
- Svelte latest
- Inertia.js latest
- shadcn-svelte latest
- superforms
- zod
- @phosphor-icons/svelte exclusively
- use pnpm only, never use npm

NEVER USE TECH STACK OF OLDER VERSIONS NOR DIFFERENT TECHNOLOGY

ALWAYS USE context7 MCP TO PULL THE LATEST DOCS OF the framework or tech you are working on immediately

Write the code as if the guy who ends up maintaining your code will be a violent psychopath who knows where you live, and follow the YAGNI principle